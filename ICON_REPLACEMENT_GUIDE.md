# 应用图标替换指南

## 图标文件位置
您提供的图标文件位于：`C:\Users\<USER>\Desktop\22.jpg`

## 替换步骤

### 1. 准备图标文件
- 将 `22.jpg` 转换为 PNG 格式
- 创建以下尺寸的图标文件：
  - 48x48 (mdpi)
  - 72x72 (hdpi) 
  - 96x96 (xhdpi)
  - 144x144 (xxhdpi)
  - 192x192 (xxxhdpi)

### 2. 替换现有图标
将生成的图标文件分别放置到以下目录：

```
app/src/main/res/mipmap-mdpi/ic_launcher.png (48x48)
app/src/main/res/mipmap-hdpi/ic_launcher.png (72x72)
app/src/main/res/mipmap-xhdpi/ic_launcher.png (96x96)
app/src/main/res/mipmap-xxhdpi/ic_launcher.png (144x144)
app/src/main/res/mipmap-xxxhdpi/ic_launcher.png (192x192)
```

同样创建圆形图标：
```
app/src/main/res/mipmap-mdpi/ic_launcher_round.png
app/src/main/res/mipmap-hdpi/ic_launcher_round.png
app/src/main/res/mipmap-xhdpi/ic_launcher_round.png
app/src/main/res/mipmap-xxhdpi/ic_launcher_round.png
app/src/main/res/mipmap-xxxhdpi/ic_launcher_round.png
```

### 3. 使用在线工具
推荐使用以下在线工具自动生成所有尺寸的图标：
- https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html
- https://icon.kitchen/

### 4. 手动创建（如果需要）
如果您有图像编辑软件，可以手动调整图片尺寸并保存为PNG格式。

## 注意事项
- 确保图标背景透明或使用适当的背景色
- 图标应该清晰且在小尺寸下仍然可识别
- 遵循Android图标设计规范
