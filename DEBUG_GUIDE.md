# 计步器应用调试指南

## 分步调试方案

### 第一步：基础环境验证

1. **检查Android Studio版本**
   - 确保使用 Android Studio Hedgehog (2023.1.1) 或更新版本
   - 更新到最新版本：Help -> Check for Updates

2. **验证JDK配置**
   - File -> Project Structure -> SDK Location
   - 确保JDK版本为11或更高
   - 如果未配置，下载并设置JDK路径

3. **清理项目**
   ```
   Build -> Clean Project
   Build -> Rebuild Project
   ```

### 第二步：简化测试

如果完整应用无法编译，先测试简化版本：

1. **临时修改AndroidManifest.xml**
   将MainActivity替换为SimpleMainActivity：
   ```xml
   <activity
       android:name=".SimpleMainActivity"
       android:exported="true"
       android:label="@string/app_name"
       android:theme="@style/Theme.Jibuqi">
   ```

2. **或者使用TestActivity**
   ```xml
   <activity
       android:name=".TestActivity"
       android:exported="true"
       android:label="@string/app_name"
       android:theme="@style/Theme.Jibuqi">
   ```

### 第三步：逐步启用功能

#### 3.1 基础UI测试
- 确保简化版本能够运行
- 验证导航功能
- 检查主题和样式

#### 3.2 添加ViewModel
- 逐步启用MainViewModel
- 测试数据流
- 验证状态管理

#### 3.3 启用传感器
- 添加传感器管理器
- 测试权限请求
- 验证传感器数据

#### 3.4 启用数据库
- 添加Room数据库
- 测试数据存储
- 验证数据查询

#### 3.5 启用后台服务
- 添加StepCounterService
- 测试前台服务
- 验证通知功能

### 第四步：常见问题解决

#### 问题1: Gradle同步失败
```bash
# 解决方案1: 清理Gradle缓存
./gradlew clean
./gradlew --stop

# 解决方案2: 删除.gradle文件夹
rm -rf .gradle
rm -rf app/build
```

#### 问题2: 依赖冲突
在app/build.gradle.kts中添加：
```kotlin
configurations.all {
    resolutionStrategy {
        force("androidx.compose.material:material-icons-core:1.5.8")
    }
}
```

#### 问题3: kapt错误
如果kapt插件有问题，可以临时禁用Room：
```kotlin
// 注释掉Room相关依赖
// implementation("androidx.room:room-runtime:2.5.0")
// implementation("androidx.room:room-ktx:2.5.0")
// kapt("androidx.room:room-compiler:2.5.0")
```

#### 问题4: 图标不存在
替换不存在的图标：
```kotlin
// 将不存在的图标替换为基础图标
Icons.Default.FlashlightOn -> Icons.Default.Flashlight
Icons.Default.LightMode -> Icons.Default.WbSunny
Icons.Default.Route -> Icons.Default.Place
```

### 第五步：运行时调试

#### 5.1 权限问题
- 在设备设置中手动授予权限
- 检查targetSdkVersion设置
- 验证权限声明

#### 5.2 传感器问题
- 在真实设备上测试（模拟器可能不支持）
- 检查设备传感器支持：
  ```kotlin
  val sensorManager = getSystemService(Context.SENSOR_SERVICE) as SensorManager
  val sensors = sensorManager.getSensorList(Sensor.TYPE_ALL)
  sensors.forEach { sensor ->
      Log.d("Sensors", "Available: ${sensor.name}")
  }
  ```

#### 5.3 服务问题
- 检查前台服务权限
- 验证通知渠道创建
- 确保服务在AndroidManifest.xml中声明

### 第六步：性能优化

#### 6.1 内存优化
- 使用LeakCanary检测内存泄漏
- 及时释放传感器资源
- 优化数据库查询

#### 6.2 电池优化
- 将应用添加到电池优化白名单
- 优化传感器采样频率
- 合理使用后台服务

### 调试工具

#### 1. Logcat过滤
```bash
adb logcat | grep -E "(StepCounter|FlashlightManager|LightSensor|MainActivity)"
```

#### 2. 数据库检查
```bash
adb shell
run-as com.example.jibuqi
ls databases/
```

#### 3. 权限检查
```bash
adb shell dumpsys package com.example.jibuqi | grep permission
```

### 备用方案

如果仍然无法解决问题：

1. **创建新项目**
   - 使用Android Studio创建新的Compose项目
   - 逐步复制代码文件
   - 确保每个步骤都能编译

2. **使用模板**
   - 基于Android官方Compose模板
   - 添加计步器功能
   - 保持简单的架构

3. **分模块开发**
   - 将功能拆分为独立模块
   - 分别测试每个模块
   - 最后整合所有功能

## 联系支持

如果按照以上步骤仍然无法解决问题：

1. 提供完整的错误日志
2. 说明具体的编译错误信息
3. 提供设备和Android版本信息
4. 描述已尝试的解决方案

记住：逐步调试是关键，不要一次性启用所有功能！
