# 计步器应用构建说明

## 构建前准备

### 1. 环境要求
- Android Studio Hedgehog (2023.1.1) 或更新版本
- JDK 11 或更高版本
- Android SDK API 24-35
- Gradle 8.0+

### 2. 修复常见编译问题

#### 问题1: Java环境未配置
如果遇到 "JAVA_HOME is not set" 错误：

**Windows:**
1. 下载并安装 JDK 11+
2. 设置环境变量：
   - JAVA_HOME = C:\Program Files\Java\jdk-11.x.x
   - 将 %JAVA_HOME%\bin 添加到 PATH

**或者在Android Studio中:**
1. File -> Project Structure -> SDK Location
2. 设置 JDK location

#### 问题2: Gradle同步失败
1. 打开 Android Studio
2. File -> Invalidate Caches and Restart
3. 等待重新索引完成
4. 点击 "Sync Project with Gradle Files"

#### 问题3: 依赖版本冲突
如果遇到依赖冲突，可以尝试：
1. 清理项目：Build -> Clean Project
2. 重新构建：Build -> Rebuild Project

### 3. 分步构建方案

#### 第一阶段：基础功能验证
1. 首先注释掉复杂功能，只保留基本UI
2. 确保应用能够启动
3. 逐步启用功能模块

#### 第二阶段：传感器功能
1. 启用步数传感器
2. 测试权限请求
3. 验证数据存储

#### 第三阶段：完整功能
1. 启用所有传感器
2. 测试后台服务
3. 验证手电筒功能

## 快速启动步骤

### 方法1: 使用Android Studio
1. 打开Android Studio
2. File -> Open -> 选择项目目录
3. 等待Gradle同步完成
4. 连接设备或启动模拟器
5. 点击 Run 按钮

### 方法2: 命令行构建
```bash
# Windows
gradlew.bat assembleDebug

# Linux/Mac
./gradlew assembleDebug
```

### 方法3: 简化测试
如果主应用无法启动，可以先测试 TestActivity：
1. 在 AndroidManifest.xml 中临时将 TestActivity 设为启动Activity
2. 验证基本Compose功能是否正常

## 故障排除

### 常见错误及解决方案

1. **编译错误: "Unresolved reference"**
   - 检查导入语句
   - 确保所有依赖都已正确添加

2. **运行时错误: "Permission denied"**
   - 检查权限声明
   - 确保运行时权限请求正常

3. **传感器不工作**
   - 在真实设备上测试（模拟器可能不支持所有传感器）
   - 检查设备是否支持相应传感器

4. **手电筒功能异常**
   - 确保设备有闪光灯
   - 检查相机权限
   - 测试在真实设备上

### 调试技巧

1. **查看日志**
   ```bash
   adb logcat | grep "StepCounter\|FlashlightManager\|LightSensor"
   ```

2. **检查权限状态**
   - 在设置中查看应用权限
   - 使用 `adb shell dumpsys package com.example.jibuqi` 查看详细信息

3. **测试传感器**
   - 使用传感器测试应用验证设备支持
   - 检查传感器精度和可用性

## 部署建议

### 开发环境
- 使用 Debug 构建类型
- 启用详细日志
- 在真实设备上测试

### 生产环境
- 使用 Release 构建类型
- 启用代码混淆
- 测试不同设备和Android版本

## 性能优化

1. **电池优化**
   - 将应用添加到电池优化白名单
   - 优化后台服务使用

2. **内存优化**
   - 及时释放传感器资源
   - 避免内存泄漏

3. **用户体验**
   - 提供清晰的权限说明
   - 优雅处理传感器不可用情况

## 联系支持

如果遇到无法解决的问题：
1. 检查 Android Studio 的 Build 输出
2. 查看 Logcat 错误信息
3. 确认设备兼容性
4. 尝试在不同设备上测试
