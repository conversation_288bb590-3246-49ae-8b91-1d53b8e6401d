# 计步器 Android 应用

一个功能完整的Android计步器应用，具有步数统计、历史记录、每日目标设置和自动手电筒功能。

## 功能特性

### 🚶‍♂️ 步数统计
- 实时步数监测
- 使用多种传感器（步数计数器、步数检测器、加速度计）
- 后台服务持续运行
- 通知栏显示当前步数

### 📊 数据统计
- 每日步数记录
- 距离计算（基于步长）
- 卡路里消耗估算
- 活动时间统计

### 🎯 目标管理
- 自定义每日步数目标
- 进度可视化（圆环进度条）
- 目标完成提醒

### 📈 历史记录
- 每日步数历史
- 周/月统计视图
- 最佳记录追踪
- 数据导出功能

### 💡 智能手电筒
- 环境光线检测
- 自动开启/关闭手电筒
- 可调节光线阈值
- 手动控制开关

### 🎨 现代化UI
- Material Design 3设计
- 深色/浅色主题支持
- 流畅的动画效果
- 直观的用户界面

## 技术架构

### 开发技术栈
- **语言**: Kotlin
- **UI框架**: Jetpack Compose
- **架构**: MVVM + Repository Pattern
- **数据库**: Room
- **异步处理**: Coroutines + Flow
- **依赖注入**: 手动依赖注入
- **导航**: Navigation Compose

### 核心组件

#### 传感器管理
- `StepSensorManager`: 步数传感器管理
- `LightSensorManager`: 光线传感器管理
- `FlashlightManager`: 手电筒控制

#### 数据层
- `StepDatabase`: Room数据库
- `StepEntity`: 步数数据实体
- `StepDao`: 数据访问对象
- `PreferencesManager`: 偏好设置管理

#### 服务层
- `StepCounterService`: 后台计步服务

#### UI层
- `MainActivity`: 主Activity
- `HomeScreen`: 主页面
- `HistoryScreen`: 历史记录页面
- `SettingsScreen`: 设置页面

## 权限要求

应用需要以下权限：

```xml
<!-- 步数统计 -->
<uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />

<!-- 位置服务（用于距离计算） -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- 手电筒功能 -->
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.FLASHLIGHT" />

<!-- 后台服务 -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_HEALTH" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

## 安装和运行

### 环境要求
- Android Studio Arctic Fox 或更高版本
- Android SDK API 24 (Android 7.0) 或更高
- Kotlin 1.9.0 或更高版本

### 构建步骤
1. 克隆项目到本地
2. 在Android Studio中打开项目
3. 等待Gradle同步完成
4. 连接Android设备或启动模拟器
5. 点击"Run"按钮构建并安装应用

### 首次运行
1. 应用会请求必要的权限
2. 授予所有权限以确保功能正常
3. 应用会自动启动计步服务
4. 开始您的步数统计之旅！

## 使用说明

### 主页面
- 查看今日步数和目标进度
- 实时显示距离和卡路里
- 控制计步服务的启动/停止
- 手动切换手电筒

### 历史记录
- 查看过去的步数记录
- 切换周/月视图
- 查看统计概览

### 设置页面
- 设置每日步数目标
- 配置自动手电筒功能
- 调整光线阈值
- 查看传感器信息

## 自定义配置

### 修改默认设置
在 `PreferencesManager.kt` 中可以修改默认值：
```kotlin
const val DEFAULT_DAILY_GOAL = 10000        // 默认每日目标
const val DEFAULT_STEP_LENGTH = 0.7f        // 默认步长（米）
const val DEFAULT_LIGHT_THRESHOLD = 10.0f   // 默认光线阈值
```

### 调整传感器灵敏度
在 `StepSensorManager.kt` 中可以调整：
```kotlin
private val stepThreshold = 12.0f           // 加速度计步数阈值
private const val STEP_BUFFER_SIZE = 10     // 步数缓冲区大小
```

## 故障排除

### 步数不准确
1. 确保已授予活动识别权限
2. 检查设备是否支持步数传感器
3. 尝试重启应用或重新校准

### 手电筒不工作
1. 确保已授予相机权限
2. 检查设备是否有闪光灯
3. 确认没有其他应用占用相机

### 后台服务停止
1. 检查电池优化设置
2. 将应用添加到白名单
3. 确保前台服务权限已授予

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发规范
- 遵循Kotlin编码规范
- 使用有意义的提交信息
- 添加适当的注释和文档
- 确保代码通过所有测试

## 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发者

---

**享受您的步数统计之旅！** 🚶‍♂️📱
