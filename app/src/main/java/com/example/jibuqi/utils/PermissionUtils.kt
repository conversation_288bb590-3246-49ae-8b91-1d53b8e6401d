package com.example.jibuqi.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat

/**
 * 权限工具类
 */
object PermissionUtils {
    
    /**
     * 检查活动识别权限
     */
    fun hasActivityRecognitionPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.ACTIVITY_RECOGNITION
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true // Android 10以下不需要此权限
        }
    }
    
    /**
     * 检查位置权限
     */
    fun hasLocationPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED ||
        ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查相机权限（用于手电筒）
     */
    fun hasCameraPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 获取所有需要的权限列表
     */
    fun getRequiredPermissions(): Array<String> {
        val permissions = mutableListOf<String>()
        
        // 活动识别权限（Android 10+）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            permissions.add(Manifest.permission.ACTIVITY_RECOGNITION)
        }
        
        // 位置权限
        permissions.add(Manifest.permission.ACCESS_FINE_LOCATION)
        permissions.add(Manifest.permission.ACCESS_COARSE_LOCATION)
        
        // 相机权限（用于手电筒）
        permissions.add(Manifest.permission.CAMERA)
        
        return permissions.toTypedArray()
    }
    
    /**
     * 检查是否所有必要权限都已授予
     */
    fun hasAllRequiredPermissions(context: Context): Boolean {
        return hasActivityRecognitionPermission(context) &&
               hasLocationPermission(context) &&
               hasCameraPermission(context)
    }
    
    /**
     * 获取未授予的权限列表
     */
    fun getMissingPermissions(context: Context): List<String> {
        val missingPermissions = mutableListOf<String>()
        
        if (!hasActivityRecognitionPermission(context) && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            missingPermissions.add(Manifest.permission.ACTIVITY_RECOGNITION)
        }
        
        if (!hasLocationPermission(context)) {
            missingPermissions.add(Manifest.permission.ACCESS_FINE_LOCATION)
        }
        
        if (!hasCameraPermission(context)) {
            missingPermissions.add(Manifest.permission.CAMERA)
        }
        
        return missingPermissions
    }
    
    /**
     * 获取权限的用户友好名称
     */
    fun getPermissionName(permission: String): String {
        return when (permission) {
            Manifest.permission.ACTIVITY_RECOGNITION -> "活动识别"
            Manifest.permission.ACCESS_FINE_LOCATION -> "精确位置"
            Manifest.permission.ACCESS_COARSE_LOCATION -> "大致位置"
            Manifest.permission.CAMERA -> "相机"
            else -> permission
        }
    }
    
    /**
     * 获取权限的说明
     */
    fun getPermissionDescription(permission: String): String {
        return when (permission) {
            Manifest.permission.ACTIVITY_RECOGNITION -> "用于检测您的步行活动和统计步数"
            Manifest.permission.ACCESS_FINE_LOCATION -> "用于计算行走距离和路径追踪"
            Manifest.permission.ACCESS_COARSE_LOCATION -> "用于计算大致的行走距离"
            Manifest.permission.CAMERA -> "用于控制手电筒功能"
            else -> "应用正常运行所需的权限"
        }
    }
}
