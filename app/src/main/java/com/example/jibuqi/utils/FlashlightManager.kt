package com.example.jibuqi.utils

import android.content.Context
import android.hardware.camera2.CameraAccessException
import android.hardware.camera2.CameraManager
import android.os.Build
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 手电筒管理器
 */
class FlashlightManager(private val context: Context) {
    
    private val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
    private var cameraId: String? = null
    
    // 手电筒状态
    private val _isFlashlightOn = MutableStateFlow(false)
    val isFlashlightOn: StateFlow<Boolean> = _isFlashlightOn.asStateFlow()
    
    private val _isFlashlightAvailable = MutableStateFlow(false)
    val isFlashlightAvailable: StateFlow<Boolean> = _isFlashlightAvailable.asStateFlow()
    
    // 回调接口
    interface FlashlightCallback {
        fun onFlashlightStateChanged(isOn: Boolean)
        fun onFlashlightError(error: String)
    }
    
    private var flashlightCallback: FlashlightCallback? = null
    
    // 相机可用性回调
    private val cameraAvailabilityCallback = object : CameraManager.AvailabilityCallback() {
        override fun onCameraAvailable(cameraId: String) {
            super.onCameraAvailable(cameraId)
            if (cameraId == <EMAIL>) {
                _isFlashlightAvailable.value = true
                Log.d(TAG, "Camera available: $cameraId")
            }
        }
        
        override fun onCameraUnavailable(cameraId: String) {
            super.onCameraUnavailable(cameraId)
            if (cameraId == <EMAIL>) {
                _isFlashlightAvailable.value = false
                Log.d(TAG, "Camera unavailable: $cameraId")
            }
        }
    }
    
    // 手电筒状态回调
    private val torchCallback = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        object : CameraManager.TorchCallback() {
            override fun onTorchModeChanged(cameraId: String, enabled: Boolean) {
                super.onTorchModeChanged(cameraId, enabled)
                if (cameraId == <EMAIL>) {
                    _isFlashlightOn.value = enabled
                    flashlightCallback?.onFlashlightStateChanged(enabled)
                    Log.d(TAG, "Torch mode changed: $enabled for camera $cameraId")
                }
            }
            
            override fun onTorchModeUnavailable(cameraId: String) {
                super.onTorchModeUnavailable(cameraId)
                if (cameraId == <EMAIL>) {
                    _isFlashlightAvailable.value = false
                    Log.d(TAG, "Torch mode unavailable for camera $cameraId")
                }
            }
        }
    } else null
    
    companion object {
        private const val TAG = "FlashlightManager"
    }
    
    init {
        initializeFlashlight()
    }
    
    private fun initializeFlashlight() {
        try {
            // 查找带有闪光灯的后置摄像头
            for (id in cameraManager.cameraIdList) {
                val characteristics = cameraManager.getCameraCharacteristics(id)
                val hasFlash = characteristics.get(android.hardware.camera2.CameraCharacteristics.FLASH_INFO_AVAILABLE)
                
                if (hasFlash == true) {
                    cameraId = id
                    _isFlashlightAvailable.value = true
                    Log.d(TAG, "Found camera with flash: $id")
                    break
                }
            }
            
            if (cameraId == null) {
                Log.w(TAG, "No camera with flash found")
                _isFlashlightAvailable.value = false
            } else {
                // 注册回调
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    torchCallback?.let { callback ->
                        cameraManager.registerTorchCallback(callback, null)
                    }
                }
                cameraManager.registerAvailabilityCallback(cameraAvailabilityCallback, null)
            }
            
        } catch (e: CameraAccessException) {
            Log.e(TAG, "Camera access exception during initialization", e)
            _isFlashlightAvailable.value = false
            flashlightCallback?.onFlashlightError("相机访问异常: ${e.message}")
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected exception during initialization", e)
            _isFlashlightAvailable.value = false
            flashlightCallback?.onFlashlightError("初始化异常: ${e.message}")
        }
    }
    
    /**
     * 设置手电筒回调
     */
    fun setFlashlightCallback(callback: FlashlightCallback?) {
        flashlightCallback = callback
    }
    
    /**
     * 开启手电筒
     */
    fun turnOnFlashlight(): Boolean {
        if (!_isFlashlightAvailable.value) {
            Log.w(TAG, "Flashlight not available")
            flashlightCallback?.onFlashlightError("手电筒不可用")
            return false
        }
        
        if (_isFlashlightOn.value) {
            Log.d(TAG, "Flashlight already on")
            return true
        }
        
        return try {
            cameraId?.let { id ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    cameraManager.setTorchMode(id, true)
                    Log.d(TAG, "Flashlight turned on")
                    true
                } else {
                    Log.w(TAG, "Flashlight not supported on this Android version")
                    flashlightCallback?.onFlashlightError("Android版本不支持手电筒功能")
                    false
                }
            } ?: run {
                Log.e(TAG, "Camera ID is null")
                flashlightCallback?.onFlashlightError("相机ID为空")
                false
            }
        } catch (e: CameraAccessException) {
            Log.e(TAG, "Failed to turn on flashlight", e)
            flashlightCallback?.onFlashlightError("开启手电筒失败: ${e.message}")
            false
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error turning on flashlight", e)
            flashlightCallback?.onFlashlightError("开启手电筒时发生未知错误: ${e.message}")
            false
        }
    }
    
    /**
     * 关闭手电筒
     */
    fun turnOffFlashlight(): Boolean {
        if (!_isFlashlightAvailable.value) {
            Log.w(TAG, "Flashlight not available")
            return false
        }
        
        if (!_isFlashlightOn.value) {
            Log.d(TAG, "Flashlight already off")
            return true
        }
        
        return try {
            cameraId?.let { id ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    cameraManager.setTorchMode(id, false)
                    Log.d(TAG, "Flashlight turned off")
                    true
                } else {
                    Log.w(TAG, "Flashlight not supported on this Android version")
                    false
                }
            } ?: run {
                Log.e(TAG, "Camera ID is null")
                false
            }
        } catch (e: CameraAccessException) {
            Log.e(TAG, "Failed to turn off flashlight", e)
            flashlightCallback?.onFlashlightError("关闭手电筒失败: ${e.message}")
            false
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error turning off flashlight", e)
            flashlightCallback?.onFlashlightError("关闭手电筒时发生未知错误: ${e.message}")
            false
        }
    }
    
    /**
     * 切换手电筒状态
     */
    fun toggleFlashlight(): Boolean {
        return if (_isFlashlightOn.value) {
            turnOffFlashlight()
        } else {
            turnOnFlashlight()
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            // 确保手电筒关闭
            if (_isFlashlightOn.value) {
                turnOffFlashlight()
            }
            
            // 注销回调
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                torchCallback?.let { callback ->
                    cameraManager.unregisterTorchCallback(callback)
                }
            }
            cameraManager.unregisterAvailabilityCallback(cameraAvailabilityCallback)
            
            Log.d(TAG, "FlashlightManager cleaned up")
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup", e)
        }
    }
    
    /**
     * 获取手电筒信息
     */
    fun getFlashlightInfo(): String {
        return if (_isFlashlightAvailable.value) {
            "手电筒可用\n相机ID: $cameraId\n当前状态: ${if (_isFlashlightOn.value) "开启" else "关闭"}"
        } else {
            "手电筒不可用"
        }
    }
}
