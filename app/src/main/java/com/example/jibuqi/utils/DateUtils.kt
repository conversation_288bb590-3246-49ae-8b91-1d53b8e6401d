package com.example.jibuqi.utils

import java.text.SimpleDateFormat
import java.util.*

/**
 * 日期工具类
 */
object DateUtils {
    
    private const val DATE_FORMAT = "yyyy-MM-dd"
    private const val DISPLAY_DATE_FORMAT = "MM月dd日"
    private const val DISPLAY_DATE_WITH_YEAR_FORMAT = "yyyy年MM月dd日"
    private const val DISPLAY_DATE_WITH_WEEKDAY_FORMAT = "MM月dd日 EEEE"
    private const val HOUR_FORMAT = "yyyy-MM-dd-HH"
    
    private val dateFormat = SimpleDateFormat(DATE_FORMAT, Locale.getDefault())
    private val displayDateFormat = SimpleDateFormat(DISPLAY_DATE_FORMAT, Locale.getDefault())
    private val displayDateWithYearFormat = SimpleDateFormat(DISPLAY_DATE_WITH_YEAR_FORMAT, Locale.getDefault())
    private val displayDateWithWeekdayFormat = SimpleDateFormat(DISPLAY_DATE_WITH_WEEKDAY_FORMAT, Locale.getDefault())
    private val hourFormat = SimpleDateFormat(HOUR_FORMAT, Locale.getDefault())
    
    /**
     * 获取当前日期字符串 (yyyy-MM-dd)
     */
    fun getCurrentDateString(): String {
        return dateFormat.format(Date())
    }
    
    /**
     * 获取当前小时字符串 (yyyy-MM-dd-HH)
     */
    fun getCurrentHourString(): String {
        return hourFormat.format(Date())
    }
    
    /**
     * 格式化日期为显示格式 (MM月dd日)
     */
    fun formatDateForDisplay(dateString: String): String {
        return try {
            val date = dateFormat.parse(dateString)
            displayDateFormat.format(date ?: Date())
        } catch (e: Exception) {
            dateString
        }
    }
    
    /**
     * 格式化日期为带年份的显示格式 (yyyy年MM月dd日)
     */
    fun formatDateWithYear(dateString: String): String {
        return try {
            val date = dateFormat.parse(dateString)
            displayDateWithYearFormat.format(date ?: Date())
        } catch (e: Exception) {
            dateString
        }
    }
    
    /**
     * 格式化日期为带星期的显示格式 (MM月dd日 星期X)
     */
    fun formatDateWithWeekday(dateString: String): String {
        return try {
            val date = dateFormat.parse(dateString)
            displayDateWithWeekdayFormat.format(date ?: Date())
        } catch (e: Exception) {
            dateString
        }
    }
    
    /**
     * 获取指定天数前的日期
     */
    fun getDateBefore(days: Int): String {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -days)
        return dateFormat.format(calendar.time)
    }
    
    /**
     * 获取指定天数后的日期
     */
    fun getDateAfter(days: Int): String {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, days)
        return dateFormat.format(calendar.time)
    }
    
    /**
     * 获取本周的开始日期（周一）
     */
    fun getWeekStartDate(): String {
        val calendar = Calendar.getInstance()
        calendar.firstDayOfWeek = Calendar.MONDAY
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
        return dateFormat.format(calendar.time)
    }
    
    /**
     * 获取本周的结束日期（周日）
     */
    fun getWeekEndDate(): String {
        val calendar = Calendar.getInstance()
        calendar.firstDayOfWeek = Calendar.MONDAY
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY)
        return dateFormat.format(calendar.time)
    }
    
    /**
     * 获取本月的开始日期
     */
    fun getMonthStartDate(): String {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        return dateFormat.format(calendar.time)
    }
    
    /**
     * 获取本月的结束日期
     */
    fun getMonthEndDate(): String {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
        return dateFormat.format(calendar.time)
    }
    
    /**
     * 计算两个日期之间的天数差
     */
    fun getDaysBetween(startDate: String, endDate: String): Int {
        return try {
            val start = dateFormat.parse(startDate)
            val end = dateFormat.parse(endDate)
            val diffInMillis = (end!!.time - start!!.time)
            (diffInMillis / (1000 * 60 * 60 * 24)).toInt()
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * 检查日期是否是今天
     */
    fun isToday(dateString: String): Boolean {
        return dateString == getCurrentDateString()
    }
    
    /**
     * 检查日期是否是昨天
     */
    fun isYesterday(dateString: String): Boolean {
        return dateString == getDateBefore(1)
    }
    
    /**
     * 获取相对日期描述
     */
    fun getRelativeDateDescription(dateString: String): String {
        return when {
            isToday(dateString) -> "今天"
            isYesterday(dateString) -> "昨天"
            dateString == getDateBefore(2) -> "前天"
            else -> formatDateWithWeekday(dateString)
        }
    }
    
    /**
     * 获取日期范围内的所有日期
     */
    fun getDateRange(startDate: String, endDate: String): List<String> {
        val dates = mutableListOf<String>()
        try {
            val start = dateFormat.parse(startDate)
            val end = dateFormat.parse(endDate)
            
            val calendar = Calendar.getInstance()
            calendar.time = start!!
            
            while (!calendar.time.after(end)) {
                dates.add(dateFormat.format(calendar.time))
                calendar.add(Calendar.DAY_OF_YEAR, 1)
            }
        } catch (e: Exception) {
            // 解析失败，返回空列表
        }
        return dates
    }
    
    /**
     * 获取最近N天的日期列表
     */
    fun getRecentDates(days: Int): List<String> {
        val dates = mutableListOf<String>()
        val calendar = Calendar.getInstance()
        
        for (i in 0 until days) {
            dates.add(dateFormat.format(calendar.time))
            calendar.add(Calendar.DAY_OF_YEAR, -1)
        }
        
        return dates.reversed() // 返回正序列表
    }
    
    /**
     * 验证日期字符串格式是否正确
     */
    fun isValidDateString(dateString: String): Boolean {
        return try {
            dateFormat.parse(dateString)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取星期几的中文名称
     */
    fun getWeekdayName(dateString: String): String {
        return try {
            val date = dateFormat.parse(dateString)
            val calendar = Calendar.getInstance()
            calendar.time = date!!
            
            when (calendar.get(Calendar.DAY_OF_WEEK)) {
                Calendar.SUNDAY -> "周日"
                Calendar.MONDAY -> "周一"
                Calendar.TUESDAY -> "周二"
                Calendar.WEDNESDAY -> "周三"
                Calendar.THURSDAY -> "周四"
                Calendar.FRIDAY -> "周五"
                Calendar.SATURDAY -> "周六"
                else -> ""
            }
        } catch (e: Exception) {
            ""
        }
    }
}
