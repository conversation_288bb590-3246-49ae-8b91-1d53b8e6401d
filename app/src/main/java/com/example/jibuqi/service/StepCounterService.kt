package com.example.jibuqi.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.example.jibuqi.R
import com.example.jibuqi.data.PreferencesManager
import com.example.jibuqi.data.StepDatabase
import com.example.jibuqi.data.StepEntity
import com.example.jibuqi.sensor.LightSensorManager
import com.example.jibuqi.sensor.StepSensorManager
import com.example.jibuqi.utils.FlashlightManager
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.first
import java.text.SimpleDateFormat
import java.util.*

/**
 * 步数统计后台服务
 */
class StepCounterService : Service() {
    
    private lateinit var stepSensorManager: StepSensorManager
    private lateinit var lightSensorManager: LightSensorManager
    private lateinit var flashlightManager: FlashlightManager
    private lateinit var preferencesManager: PreferencesManager
    private lateinit var stepDatabase: StepDatabase
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var isServiceRunning = false
    
    // 当前日期，用于检测日期变化
    private var currentDate = getCurrentDateString()
    private var dailySteps = 0
    
    companion object {
        private const val TAG = "StepCounterService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "step_counter_channel"
        private const val ACTION_START_SERVICE = "START_SERVICE"
        private const val ACTION_STOP_SERVICE = "STOP_SERVICE"
        
        fun startService(context: Context) {
            val intent = Intent(context, StepCounterService::class.java).apply {
                action = ACTION_START_SERVICE
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, StepCounterService::class.java).apply {
                action = ACTION_STOP_SERVICE
            }
            context.stopService(intent)
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "Service created")
        
        // 初始化组件
        stepSensorManager = StepSensorManager(this)
        lightSensorManager = LightSensorManager(this)
        flashlightManager = FlashlightManager(this)
        preferencesManager = PreferencesManager(this)
        stepDatabase = StepDatabase.getDatabase(this)
        
        // 创建通知渠道
        createNotificationChannel()
        
        // 设置传感器监听器
        setupSensorListeners()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_SERVICE -> {
                startForegroundService()
            }
            ACTION_STOP_SERVICE -> {
                stopSelf()
            }
        }
        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun startForegroundService() {
        if (isServiceRunning) return
        
        Log.d(TAG, "Starting foreground service")
        
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)
        
        // 开始传感器监听
        stepSensorManager.startListening()
        lightSensorManager.startListening()
        
        // 启动数据收集协程
        startDataCollection()
        
        isServiceRunning = true
    }
    
    private fun startDataCollection() {
        serviceScope.launch {
            // 初始化当日步数
            initializeDailySteps()
            
            // 监听步数变化
            stepSensorManager.dailySteps.collect { steps ->
                dailySteps = steps
                updateNotification()
                saveDailySteps(steps)
                
                // 检查日期是否变化
                checkDateChange()
            }
        }
    }
    
    private suspend fun initializeDailySteps() {
        try {
            val today = getCurrentDateString()
            val todayRecord = stepDatabase.stepDao().getStepRecordByDate(today)
            
            if (todayRecord != null) {
                // 如果今天已有记录，设置为初始步数
                stepSensorManager.setInitialStepCount(todayRecord.steps)
                dailySteps = todayRecord.steps
            } else {
                // 新的一天，重置步数
                stepSensorManager.resetDailySteps()
                dailySteps = 0
            }
            
            Log.d(TAG, "Daily steps initialized: $dailySteps")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing daily steps", e)
        }
    }
    
    private suspend fun saveDailySteps(steps: Int) {
        try {
            val today = getCurrentDateString()
            val dailyGoal = preferencesManager.dailyGoal.first()
            val stepLength = preferencesManager.stepLength.first()
            val weight = preferencesManager.weight.first()
            
            // 计算距离和卡路里
            val distance = steps * stepLength // 米
            val calories = calculateCalories(steps, weight)
            
            val stepEntity = StepEntity(
                date = today,
                steps = steps,
                distance = distance,
                calories = calories,
                dailyGoal = dailyGoal
            )
            
            stepDatabase.stepDao().insertOrUpdateStepRecord(stepEntity)
            Log.v(TAG, "Steps saved: $steps for date $today")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving daily steps", e)
        }
    }
    
    private fun calculateCalories(steps: Int, weight: Float): Float {
        // 简单的卡路里计算公式：步数 * 体重(kg) * 0.0005
        return steps * weight * 0.0005f
    }
    
    private fun checkDateChange() {
        val newDate = getCurrentDateString()
        if (newDate != currentDate) {
            Log.d(TAG, "Date changed from $currentDate to $newDate")
            currentDate = newDate
            
            // 重置当日步数
            stepSensorManager.resetDailySteps()
            dailySteps = 0
            
            // 更新通知
            updateNotification()
        }
    }
    
    private fun setupSensorListeners() {
        // 设置光线传感器监听器
        lightSensorManager.setLightChangeListener(object : LightSensorManager.LightChangeListener {
            override fun onLightChanged(lightLevel: Float, isDark: Boolean) {
                // 可以在这里记录光线变化日志
            }
            
            override fun onDarkDetected() {
                serviceScope.launch {
                    val flashlightEnabled = preferencesManager.flashlightEnabled.first()
                    if (flashlightEnabled) {
                        flashlightManager.turnOnFlashlight()
                        Log.d(TAG, "Auto flashlight turned on due to dark environment")
                    }
                }
            }
            
            override fun onLightDetected() {
                serviceScope.launch {
                    val flashlightEnabled = preferencesManager.flashlightEnabled.first()
                    if (flashlightEnabled && flashlightManager.isFlashlightOn.value) {
                        flashlightManager.turnOffFlashlight()
                        Log.d(TAG, "Auto flashlight turned off due to light environment")
                    }
                }
            }
        })
        
        // 设置手电筒回调
        flashlightManager.setFlashlightCallback(object : FlashlightManager.FlashlightCallback {
            override fun onFlashlightStateChanged(isOn: Boolean) {
                Log.d(TAG, "Flashlight state changed: $isOn")
            }
            
            override fun onFlashlightError(error: String) {
                Log.e(TAG, "Flashlight error: $error")
            }
        })
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                getString(R.string.notification_channel_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.notification_channel_desc)
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        val intent = packageManager.getLaunchIntentForPackage(packageName)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.step_service_title))
            .setContentText(getString(R.string.step_service_desc))
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .build()
    }
    
    private fun updateNotification() {
        if (!isServiceRunning) return
        
        val intent = packageManager.getLaunchIntentForPackage(packageName)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("${getString(R.string.today_steps)}: $dailySteps")
            .setContentText(getString(R.string.step_service_desc))
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .build()
        
        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "Service destroyed")
        
        isServiceRunning = false
        
        // 停止传感器监听
        stepSensorManager.stopListening()
        lightSensorManager.stopListening()
        
        // 清理资源
        flashlightManager.cleanup()
        
        // 取消协程
        serviceScope.cancel()
    }
    
    private fun getCurrentDateString(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return dateFormat.format(Date())
    }
}
