package com.example.jibuqi.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.jibuqi.data.PreferencesManager
import com.example.jibuqi.data.StepDatabase
import com.example.jibuqi.data.StepEntity
import com.example.jibuqi.sensor.LightSensorManager
import com.example.jibuqi.sensor.StepSensorManager
import com.example.jibuqi.service.StepCounterService
import com.example.jibuqi.utils.FlashlightManager
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 主ViewModel
 */
class MainViewModel(application: Application) : AndroidViewModel(application) {
    
    private val context = application.applicationContext
    private val preferencesManager = PreferencesManager(context)
    private val stepDatabase = StepDatabase.getDatabase(context)
    private val stepDao = stepDatabase.stepDao()
    
    // 传感器管理器
    val stepSensorManager = StepSensorManager(context)
    val lightSensorManager = LightSensorManager(context)
    val flashlightManager = FlashlightManager(context)
    
    // UI状态
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    // 今日步数数据
    private val todayDate = getCurrentDateString()
    val todayStepRecord: StateFlow<StepEntity?> = stepDao.getStepRecordByDateFlow(todayDate)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )
    
    // 偏好设置
    val dailyGoal = preferencesManager.dailyGoal
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = PreferencesManager.DEFAULT_DAILY_GOAL
        )
    
    val flashlightEnabled = preferencesManager.flashlightEnabled
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = PreferencesManager.DEFAULT_FLASHLIGHT_ENABLED
        )
    
    val lightThreshold = preferencesManager.lightThreshold
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = PreferencesManager.DEFAULT_LIGHT_THRESHOLD
        )
    
    init {
        initializeViewModel()
    }
    
    private fun initializeViewModel() {
        viewModelScope.launch {
            // 设置光线阈值
            lightThreshold.collect { threshold ->
                lightSensorManager.setLightThreshold(threshold)
            }
        }
        
        // 监听传感器状态
        viewModelScope.launch {
            combine(
                stepSensorManager.isActive,
                lightSensorManager.isActive,
                flashlightManager.isFlashlightAvailable
            ) { stepActive, lightActive, flashAvailable ->
                _uiState.value = _uiState.value.copy(
                    isStepSensorActive = stepActive,
                    isLightSensorActive = lightActive,
                    isFlashlightAvailable = flashAvailable
                )
            }.collect()
        }
        
        // 监听光线和手电筒状态
        viewModelScope.launch {
            combine(
                lightSensorManager.lightLevel,
                lightSensorManager.isDark,
                flashlightManager.isFlashlightOn
            ) { lightLevel, isDark, flashOn ->
                _uiState.value = _uiState.value.copy(
                    lightLevel = lightLevel,
                    isDark = isDark,
                    isFlashlightOn = flashOn
                )
            }.collect()
        }
    }
    
    /**
     * 启动步数统计服务
     */
    fun startStepCounterService() {
        StepCounterService.startService(context)
        _uiState.value = _uiState.value.copy(isServiceRunning = true)
    }
    
    /**
     * 停止步数统计服务
     */
    fun stopStepCounterService() {
        StepCounterService.stopService(context)
        _uiState.value = _uiState.value.copy(isServiceRunning = false)
    }
    
    /**
     * 设置每日目标
     */
    fun setDailyGoal(goal: Int) {
        viewModelScope.launch {
            preferencesManager.setDailyGoal(goal)
        }
    }
    
    /**
     * 设置自动手电筒功能
     */
    fun setFlashlightEnabled(enabled: Boolean) {
        viewModelScope.launch {
            preferencesManager.setFlashlightEnabled(enabled)
        }
    }
    
    /**
     * 设置光线阈值
     */
    fun setLightThreshold(threshold: Float) {
        viewModelScope.launch {
            preferencesManager.setLightThreshold(threshold)
        }
    }
    
    /**
     * 手动切换手电筒
     */
    fun toggleFlashlight() {
        flashlightManager.toggleFlashlight()
    }
    
    /**
     * 开始传感器监听（用于测试）
     */
    fun startSensorListening() {
        stepSensorManager.startListening()
        lightSensorManager.startListening()
    }
    
    /**
     * 停止传感器监听
     */
    fun stopSensorListening() {
        stepSensorManager.stopListening()
        lightSensorManager.stopListening()
    }
    
    /**
     * 获取最近几天的步数记录
     */
    fun getRecentStepRecords(days: Int): Flow<List<StepEntity>> {
        return stepDao.getRecentStepRecordsFlow(days)
    }
    
    /**
     * 获取指定日期范围的步数记录
     */
    fun getStepRecordsBetweenDates(startDate: String, endDate: String): Flow<List<StepEntity>> {
        return stepDao.getStepRecordsBetweenDatesFlow(startDate, endDate)
    }
    
    /**
     * 计算步数完成百分比
     */
    fun getStepProgress(steps: Int, goal: Int): Float {
        return if (goal > 0) (steps.toFloat() / goal.toFloat()).coerceAtMost(1.0f) else 0f
    }
    
    /**
     * 格式化距离显示
     */
    fun formatDistance(meters: Float): String {
        return if (meters >= 1000) {
            String.format("%.2f km", meters / 1000)
        } else {
            String.format("%.0f m", meters)
        }
    }
    
    /**
     * 格式化卡路里显示
     */
    fun formatCalories(calories: Float): String {
        return String.format("%.1f", calories)
    }
    
    /**
     * 获取光线级别描述
     */
    fun getLightLevelDescription(lightLevel: Float): String {
        return lightSensorManager.getLightLevelDescription(lightLevel)
    }
    
    /**
     * 检查传感器支持情况
     */
    fun checkSensorSupport(): SensorSupportInfo {
        return SensorSupportInfo(
            hasStepSensor = stepSensorManager.hasSensorSupport(),
            hasLightSensor = lightSensorManager.hasSensorSupport(),
            hasFlashlight = flashlightManager.isFlashlightAvailable.value,
            stepSensorInfo = stepSensorManager.getSensorInfo(),
            lightSensorInfo = lightSensorManager.getSensorInfo(),
            flashlightInfo = flashlightManager.getFlashlightInfo()
        )
    }
    
    override fun onCleared() {
        super.onCleared()
        // 清理资源
        stepSensorManager.stopListening()
        lightSensorManager.stopListening()
        flashlightManager.cleanup()
    }
    
    private fun getCurrentDateString(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return dateFormat.format(Date())
    }
}

/**
 * 主界面UI状态
 */
data class MainUiState(
    val isServiceRunning: Boolean = false,
    val isStepSensorActive: Boolean = false,
    val isLightSensorActive: Boolean = false,
    val isFlashlightAvailable: Boolean = false,
    val isFlashlightOn: Boolean = false,
    val lightLevel: Float = 0f,
    val isDark: Boolean = false,
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)

/**
 * 传感器支持信息
 */
data class SensorSupportInfo(
    val hasStepSensor: Boolean,
    val hasLightSensor: Boolean,
    val hasFlashlight: Boolean,
    val stepSensorInfo: String,
    val lightSensorInfo: String,
    val flashlightInfo: String
)
