package com.example.jibuqi.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.jibuqi.data.StepDatabase
import com.example.jibuqi.data.StepEntity
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 历史记录ViewModel
 */
class HistoryViewModel(application: Application) : AndroidViewModel(application) {
    
    private val stepDatabase = StepDatabase.getDatabase(application.applicationContext)
    private val stepDao = stepDatabase.stepDao()
    
    // UI状态
    private val _uiState = MutableStateFlow(HistoryUiState())
    val uiState: StateFlow<HistoryUiState> = _uiState.asStateFlow()
    
    // 最近7天的数据
    val weeklyData: StateFlow<List<StepEntity>> = stepDao.getRecentStepRecordsFlow(7)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    // 最近30天的数据
    val monthlyData: StateFlow<List<StepEntity>> = stepDao.getRecentStepRecordsFlow(30)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    /**
     * 获取指定日期范围的数据
     */
    fun getDataBetweenDates(startDate: String, endDate: String): Flow<List<StepEntity>> {
        return stepDao.getStepRecordsBetweenDatesFlow(startDate, endDate)
    }
    
    /**
     * 获取统计信息
     */
    fun getStatistics(data: List<StepEntity>): HistoryStatistics {
        if (data.isEmpty()) {
            return HistoryStatistics()
        }
        
        val totalSteps = data.sumOf { it.steps }
        val totalDistance = data.sumOf { it.distance.toDouble() }.toFloat()
        val totalCalories = data.sumOf { it.calories.toDouble() }.toFloat()
        val activeDays = data.count { it.steps > 0 }
        val averageSteps = if (data.isNotEmpty()) totalSteps / data.size else 0
        val maxSteps = data.maxOfOrNull { it.steps } ?: 0
        val minSteps = data.filter { it.steps > 0 }.minOfOrNull { it.steps } ?: 0
        
        // 计算目标完成天数
        val goalCompletedDays = data.count { it.steps >= it.dailyGoal }
        val goalCompletionRate = if (activeDays > 0) {
            (goalCompletedDays.toFloat() / activeDays.toFloat() * 100).toInt()
        } else 0
        
        return HistoryStatistics(
            totalSteps = totalSteps,
            totalDistance = totalDistance,
            totalCalories = totalCalories,
            activeDays = activeDays,
            averageSteps = averageSteps,
            maxSteps = maxSteps,
            minSteps = minSteps,
            goalCompletedDays = goalCompletedDays,
            goalCompletionRate = goalCompletionRate,
            totalDays = data.size
        )
    }
    
    /**
     * 获取本周统计
     */
    fun getWeeklyStatistics(): Flow<HistoryStatistics> {
        return weeklyData.map { data -> getStatistics(data) }
    }
    
    /**
     * 获取本月统计
     */
    fun getMonthlyStatistics(): Flow<HistoryStatistics> {
        return monthlyData.map { data -> getStatistics(data) }
    }
    
    /**
     * 获取最佳记录
     */
    fun getBestRecords(): Flow<BestRecords> {
        return stepDao.getRecentStepRecordsFlow(365) // 获取一年内的数据
            .map { data ->
                if (data.isEmpty()) {
                    BestRecords()
                } else {
                    val bestStepsRecord = data.maxByOrNull { it.steps }
                    val bestDistanceRecord = data.maxByOrNull { it.distance }
                    val bestCaloriesRecord = data.maxByOrNull { it.calories }
                    
                    BestRecords(
                        bestStepsRecord = bestStepsRecord,
                        bestDistanceRecord = bestDistanceRecord,
                        bestCaloriesRecord = bestCaloriesRecord,
                        longestStreak = calculateLongestStreak(data)
                    )
                }
            }
    }
    
    /**
     * 计算最长连续天数
     */
    private fun calculateLongestStreak(data: List<StepEntity>): Int {
        if (data.isEmpty()) return 0
        
        val sortedData = data.sortedBy { it.date }
        var currentStreak = 0
        var maxStreak = 0
        var previousDate: Date? = null
        
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        
        for (record in sortedData) {
            if (record.steps > 0) { // 只计算有步数的天数
                try {
                    val currentDate = dateFormat.parse(record.date)
                    
                    if (previousDate == null) {
                        currentStreak = 1
                    } else {
                        val daysDiff = ((currentDate!!.time - previousDate.time) / (1000 * 60 * 60 * 24)).toInt()
                        if (daysDiff == 1) {
                            currentStreak++
                        } else {
                            currentStreak = 1
                        }
                    }
                    
                    maxStreak = maxOf(maxStreak, currentStreak)
                    previousDate = currentDate
                } catch (e: Exception) {
                    // 日期解析失败，跳过
                    continue
                }
            } else {
                currentStreak = 0
            }
        }
        
        return maxStreak
    }
    
    /**
     * 删除指定日期的记录
     */
    fun deleteRecord(date: String) {
        viewModelScope.launch {
            try {
                stepDao.deleteStepRecordByDate(date)
                _uiState.value = _uiState.value.copy(
                    message = "记录已删除"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "删除失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 清除所有历史记录
     */
    fun clearAllHistory() {
        viewModelScope.launch {
            try {
                stepDao.deleteAllStepRecords()
                stepDao.deleteAllHourlySteps()
                _uiState.value = _uiState.value.copy(
                    message = "所有历史记录已清除"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "清除失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 清除消息
     */
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(
            message = null,
            errorMessage = null
        )
    }
    
    /**
     * 导出数据（返回CSV格式字符串）
     */
    suspend fun exportData(): String {
        return try {
            val allData = stepDao.getRecentStepRecords(365)
            val csvHeader = "日期,步数,距离(米),卡路里,目标步数,完成率\n"
            val csvData = allData.joinToString("\n") { record ->
                val completionRate = if (record.dailyGoal > 0) {
                    (record.steps.toFloat() / record.dailyGoal.toFloat() * 100).toInt()
                } else 0
                "${record.date},${record.steps},${record.distance},${record.calories},${record.dailyGoal},$completionRate%"
            }
            csvHeader + csvData
        } catch (e: Exception) {
            "导出失败: ${e.message}"
        }
    }
}

/**
 * 历史记录UI状态
 */
data class HistoryUiState(
    val isLoading: Boolean = false,
    val message: String? = null,
    val errorMessage: String? = null
)

/**
 * 历史统计信息
 */
data class HistoryStatistics(
    val totalSteps: Int = 0,
    val totalDistance: Float = 0f,
    val totalCalories: Float = 0f,
    val activeDays: Int = 0,
    val averageSteps: Int = 0,
    val maxSteps: Int = 0,
    val minSteps: Int = 0,
    val goalCompletedDays: Int = 0,
    val goalCompletionRate: Int = 0,
    val totalDays: Int = 0
)

/**
 * 最佳记录
 */
data class BestRecords(
    val bestStepsRecord: StepEntity? = null,
    val bestDistanceRecord: StepEntity? = null,
    val bestCaloriesRecord: StepEntity? = null,
    val longestStreak: Int = 0
)
