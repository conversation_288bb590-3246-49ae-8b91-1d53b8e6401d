package com.example.jibuqi.sensor

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 步数传感器管理器
 */
class StepSensorManager(private val context: Context) : SensorEventListener {
    
    private val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private var stepCounterSensor: Sensor? = null
    private var stepDetectorSensor: Sensor? = null
    private var accelerometerSensor: Sensor? = null
    
    // 步数相关状态
    private val _totalSteps = MutableStateFlow(0)
    val totalSteps: StateFlow<Int> = _totalSteps.asStateFlow()
    
    private val _dailySteps = MutableStateFlow(0)
    val dailySteps: StateFlow<Int> = _dailySteps.asStateFlow()
    
    private val _isActive = MutableStateFlow(false)
    val isActive: StateFlow<Boolean> = _isActive.asStateFlow()
    
    // 用于计算步数的变量
    private var initialStepCount = 0
    private var lastStepCount = 0
    private var isInitialized = false
    
    // 加速度计相关（备用方案）
    private var lastAcceleration = 0f
    private var currentAcceleration = 0f
    private var acceleration = 0f
    private val stepThreshold = 12.0f
    private var stepBuffer = 0
    
    companion object {
        private const val TAG = "StepSensorManager"
        private const val STEP_BUFFER_SIZE = 10
    }
    
    init {
        initializeSensors()
    }
    
    private fun initializeSensors() {
        // 优先使用步数计数器传感器
        stepCounterSensor = sensorManager.getDefaultSensor(Sensor.TYPE_STEP_COUNTER)
        stepDetectorSensor = sensorManager.getDefaultSensor(Sensor.TYPE_STEP_DETECTOR)
        accelerometerSensor = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
        
        Log.d(TAG, "Step Counter Sensor: ${stepCounterSensor != null}")
        Log.d(TAG, "Step Detector Sensor: ${stepDetectorSensor != null}")
        Log.d(TAG, "Accelerometer Sensor: ${accelerometerSensor != null}")
    }
    
    /**
     * 开始监听传感器
     */
    fun startListening() {
        if (_isActive.value) return
        
        var sensorRegistered = false
        
        // 优先使用步数计数器
        stepCounterSensor?.let { sensor ->
            val registered = sensorManager.registerListener(
                this, sensor, SensorManager.SENSOR_DELAY_UI
            )
            if (registered) {
                sensorRegistered = true
                Log.d(TAG, "Step counter sensor registered")
            }
        }
        
        // 如果步数计数器不可用，使用步数检测器
        if (!sensorRegistered) {
            stepDetectorSensor?.let { sensor ->
                val registered = sensorManager.registerListener(
                    this, sensor, SensorManager.SENSOR_DELAY_UI
                )
                if (registered) {
                    sensorRegistered = true
                    Log.d(TAG, "Step detector sensor registered")
                }
            }
        }
        
        // 如果都不可用，使用加速度计作为备用方案
        if (!sensorRegistered) {
            accelerometerSensor?.let { sensor ->
                val registered = sensorManager.registerListener(
                    this, sensor, SensorManager.SENSOR_DELAY_UI
                )
                if (registered) {
                    sensorRegistered = true
                    Log.d(TAG, "Accelerometer sensor registered as fallback")
                }
            }
        }
        
        if (sensorRegistered) {
            _isActive.value = true
            Log.d(TAG, "Step sensor listening started")
        } else {
            Log.e(TAG, "No suitable sensor available for step counting")
        }
    }
    
    /**
     * 停止监听传感器
     */
    fun stopListening() {
        if (!_isActive.value) return
        
        sensorManager.unregisterListener(this)
        _isActive.value = false
        Log.d(TAG, "Step sensor listening stopped")
    }
    
    /**
     * 设置初始步数（用于计算当日步数）
     */
    fun setInitialStepCount(initialCount: Int) {
        initialStepCount = initialCount
        lastStepCount = initialCount
        isInitialized = true
        Log.d(TAG, "Initial step count set to: $initialCount")
    }
    
    /**
     * 重置当日步数
     */
    fun resetDailySteps() {
        initialStepCount = _totalSteps.value
        lastStepCount = _totalSteps.value
        _dailySteps.value = 0
        Log.d(TAG, "Daily steps reset")
    }
    
    override fun onSensorChanged(event: SensorEvent?) {
        event?.let { sensorEvent ->
            when (sensorEvent.sensor.type) {
                Sensor.TYPE_STEP_COUNTER -> {
                    handleStepCounter(sensorEvent.values[0].toInt())
                }
                Sensor.TYPE_STEP_DETECTOR -> {
                    handleStepDetector()
                }
                Sensor.TYPE_ACCELEROMETER -> {
                    handleAccelerometer(sensorEvent.values)
                }
            }
        }
    }
    
    private fun handleStepCounter(stepCount: Int) {
        _totalSteps.value = stepCount
        
        if (isInitialized) {
            val dailyCount = stepCount - initialStepCount
            _dailySteps.value = maxOf(0, dailyCount)
        } else {
            // 首次获取步数，设置为初始值
            setInitialStepCount(stepCount)
        }
        
        Log.d(TAG, "Step counter: total=$stepCount, daily=${_dailySteps.value}")
    }
    
    private fun handleStepDetector() {
        // 步数检测器每检测到一步就触发一次
        val currentDaily = _dailySteps.value + 1
        _dailySteps.value = currentDaily
        _totalSteps.value = initialStepCount + currentDaily
        
        Log.d(TAG, "Step detected: daily=${_dailySteps.value}")
    }
    
    private fun handleAccelerometer(values: FloatArray) {
        // 使用加速度计数据计算步数（简单算法）
        val x = values[0]
        val y = values[1]
        val z = values[2]
        
        lastAcceleration = currentAcceleration
        currentAcceleration = kotlin.math.sqrt((x * x + y * y + z * z).toDouble()).toFloat()
        val delta = currentAcceleration - lastAcceleration
        acceleration = acceleration * 0.9f + delta
        
        if (acceleration > stepThreshold) {
            stepBuffer++
            if (stepBuffer >= STEP_BUFFER_SIZE) {
                stepBuffer = 0
                val currentDaily = _dailySteps.value + 1
                _dailySteps.value = currentDaily
                _totalSteps.value = initialStepCount + currentDaily
                
                Log.d(TAG, "Step detected via accelerometer: daily=${_dailySteps.value}")
            }
        }
    }
    
    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        Log.d(TAG, "Sensor accuracy changed: ${sensor?.name}, accuracy: $accuracy")
    }
    
    /**
     * 检查是否有可用的传感器
     */
    fun hasSensorSupport(): Boolean {
        return stepCounterSensor != null || stepDetectorSensor != null || accelerometerSensor != null
    }
    
    /**
     * 获取传感器信息
     */
    fun getSensorInfo(): String {
        val info = StringBuilder()
        stepCounterSensor?.let {
            info.append("步数计数器: ${it.name}\n")
        }
        stepDetectorSensor?.let {
            info.append("步数检测器: ${it.name}\n")
        }
        accelerometerSensor?.let {
            info.append("加速度计: ${it.name}\n")
        }
        return info.toString().ifEmpty { "无可用传感器" }
    }
}
