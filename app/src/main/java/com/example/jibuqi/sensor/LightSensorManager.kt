package com.example.jibuqi.sensor

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 光线传感器管理器
 */
class LightSensorManager(private val context: Context) : SensorEventListener {
    
    private val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private var lightSensor: Sensor? = null
    
    // 光线相关状态
    private val _lightLevel = MutableStateFlow(0f)
    val lightLevel: StateFlow<Float> = _lightLevel.asStateFlow()
    
    private val _isDark = MutableStateFlow(false)
    val isDark: StateFlow<Boolean> = _isDark.asStateFlow()
    
    private val _isActive = MutableStateFlow(false)
    val isActive: StateFlow<Boolean> = _isActive.asStateFlow()
    
    // 光线阈值（lux）
    private var lightThreshold = 10.0f
    
    // 回调接口
    interface LightChangeListener {
        fun onLightChanged(lightLevel: Float, isDark: Boolean)
        fun onDarkDetected()
        fun onLightDetected()
    }
    
    private var lightChangeListener: LightChangeListener? = null
    
    companion object {
        private const val TAG = "LightSensorManager"
        private const val DEFAULT_LIGHT_THRESHOLD = 10.0f // lux
        private const val SENSOR_DELAY = SensorManager.SENSOR_DELAY_NORMAL
    }
    
    init {
        initializeSensor()
    }
    
    private fun initializeSensor() {
        lightSensor = sensorManager.getDefaultSensor(Sensor.TYPE_LIGHT)
        Log.d(TAG, "Light sensor available: ${lightSensor != null}")
        
        lightSensor?.let { sensor ->
            Log.d(TAG, "Light sensor info: ${sensor.name}, max range: ${sensor.maximumRange} lux")
        }
    }
    
    /**
     * 设置光线阈值
     */
    fun setLightThreshold(threshold: Float) {
        lightThreshold = threshold
        // 重新评估当前光线状态
        updateDarkState(_lightLevel.value)
        Log.d(TAG, "Light threshold set to: $threshold lux")
    }
    
    /**
     * 设置光线变化监听器
     */
    fun setLightChangeListener(listener: LightChangeListener?) {
        lightChangeListener = listener
    }
    
    /**
     * 开始监听光线传感器
     */
    fun startListening() {
        if (_isActive.value) return
        
        lightSensor?.let { sensor ->
            val registered = sensorManager.registerListener(
                this, sensor, SENSOR_DELAY
            )
            if (registered) {
                _isActive.value = true
                Log.d(TAG, "Light sensor listening started")
            } else {
                Log.e(TAG, "Failed to register light sensor listener")
            }
        } ?: run {
            Log.w(TAG, "Light sensor not available")
        }
    }
    
    /**
     * 停止监听光线传感器
     */
    fun stopListening() {
        if (!_isActive.value) return
        
        sensorManager.unregisterListener(this)
        _isActive.value = false
        Log.d(TAG, "Light sensor listening stopped")
    }
    
    override fun onSensorChanged(event: SensorEvent?) {
        event?.let { sensorEvent ->
            if (sensorEvent.sensor.type == Sensor.TYPE_LIGHT) {
                val lightValue = sensorEvent.values[0]
                handleLightChange(lightValue)
            }
        }
    }
    
    private fun handleLightChange(lightValue: Float) {
        val previousDarkState = _isDark.value
        
        _lightLevel.value = lightValue
        updateDarkState(lightValue)
        
        // 通知监听器
        lightChangeListener?.onLightChanged(lightValue, _isDark.value)
        
        // 如果暗度状态发生变化，触发相应回调
        if (previousDarkState != _isDark.value) {
            if (_isDark.value) {
                lightChangeListener?.onDarkDetected()
                Log.d(TAG, "Dark environment detected: ${lightValue} lux")
            } else {
                lightChangeListener?.onLightDetected()
                Log.d(TAG, "Light environment detected: ${lightValue} lux")
            }
        }
        
        Log.v(TAG, "Light level: ${lightValue} lux, isDark: ${_isDark.value}")
    }
    
    private fun updateDarkState(lightValue: Float) {
        _isDark.value = lightValue < lightThreshold
    }
    
    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        Log.d(TAG, "Light sensor accuracy changed: $accuracy")
    }
    
    /**
     * 检查是否有光线传感器支持
     */
    fun hasSensorSupport(): Boolean {
        return lightSensor != null
    }
    
    /**
     * 获取传感器信息
     */
    fun getSensorInfo(): String {
        return lightSensor?.let { sensor ->
            "光线传感器: ${sensor.name}\n" +
            "最大范围: ${sensor.maximumRange} lux\n" +
            "分辨率: ${sensor.resolution} lux\n" +
            "功耗: ${sensor.power} mA"
        } ?: "无光线传感器"
    }
    
    /**
     * 获取当前光线级别描述
     */
    fun getLightLevelDescription(lightLevel: Float): String {
        return when {
            lightLevel < 1 -> "极暗"
            lightLevel < 10 -> "很暗"
            lightLevel < 50 -> "暗"
            lightLevel < 200 -> "室内光线"
            lightLevel < 500 -> "明亮室内"
            lightLevel < 1000 -> "很亮"
            lightLevel < 10000 -> "日光"
            else -> "强烈日光"
        }
    }
    
    /**
     * 手动触发光线检测（用于测试）
     */
    fun triggerLightDetection() {
        if (_isActive.value) {
            // 传感器正在运行，等待下一次读数
            Log.d(TAG, "Light detection triggered, waiting for sensor reading...")
        } else {
            Log.w(TAG, "Light sensor is not active, cannot trigger detection")
        }
    }
}
