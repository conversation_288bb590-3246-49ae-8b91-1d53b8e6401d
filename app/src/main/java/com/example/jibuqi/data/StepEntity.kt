package com.example.jibuqi.data

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

/**
 * 步数数据实体类
 */
@Entity(tableName = "step_records")
data class StepEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    // 日期（年月日，用于按天统计）
    val date: String, // 格式: "2024-01-01"
    
    // 当天总步数
    val steps: Int,
    
    // 距离（米）
    val distance: Float = 0f,
    
    // 消耗卡路里
    val calories: Float = 0f,
    
    // 活动时间（分钟）
    val activeMinutes: Int = 0,
    
    // 记录创建时间
    val timestamp: Long = System.currentTimeMillis(),
    
    // 当天目标步数
    val dailyGoal: Int = 10000
)

/**
 * 每小时步数记录（用于详细统计）
 */
@Entity(tableName = "hourly_steps")
data class HourlyStepEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    // 日期时间（精确到小时）
    val dateHour: String, // 格式: "2024-01-01-14" (14点)
    
    // 该小时的步数
    val steps: Int,
    
    // 记录时间戳
    val timestamp: Long = System.currentTimeMillis()
)
