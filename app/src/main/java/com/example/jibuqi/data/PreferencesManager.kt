package com.example.jibuqi.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * 偏好设置管理器
 */
class PreferencesManager(private val context: Context) {
    
    companion object {
        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "step_preferences")
        
        // 偏好设置键
        private val DAILY_GOAL_KEY = intPreferencesKey("daily_goal")
        private val FLASHLIGHT_ENABLED_KEY = booleanPreferencesKey("flashlight_enabled")
        private val LIGHT_THRESHOLD_KEY = floatPreferencesKey("light_threshold")
        private val STEP_LENGTH_KEY = floatPreferencesKey("step_length") // 步长（米）
        private val WEIGHT_KEY = floatPreferencesKey("weight") // 体重（公斤）
        private val HEIGHT_KEY = floatPreferencesKey("height") // 身高（厘米）
        private val FIRST_LAUNCH_KEY = booleanPreferencesKey("first_launch")
        private val SERVICE_ENABLED_KEY = booleanPreferencesKey("service_enabled")
        
        // 默认值
        const val DEFAULT_DAILY_GOAL = 10000
        const val DEFAULT_FLASHLIGHT_ENABLED = false
        const val DEFAULT_LIGHT_THRESHOLD = 10.0f // lux
        const val DEFAULT_STEP_LENGTH = 0.7f // 米
        const val DEFAULT_WEIGHT = 65.0f // 公斤
        const val DEFAULT_HEIGHT = 170.0f // 厘米
        const val DEFAULT_FIRST_LAUNCH = true
        const val DEFAULT_SERVICE_ENABLED = true
    }
    
    // 每日目标步数
    val dailyGoal: Flow<Int> = context.dataStore.data.map { preferences ->
        preferences[DAILY_GOAL_KEY] ?: DEFAULT_DAILY_GOAL
    }
    
    suspend fun setDailyGoal(goal: Int) {
        context.dataStore.edit { preferences ->
            preferences[DAILY_GOAL_KEY] = goal
        }
    }
    
    // 自动手电筒功能
    val flashlightEnabled: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[FLASHLIGHT_ENABLED_KEY] ?: DEFAULT_FLASHLIGHT_ENABLED
    }
    
    suspend fun setFlashlightEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[FLASHLIGHT_ENABLED_KEY] = enabled
        }
    }
    
    // 光线阈值
    val lightThreshold: Flow<Float> = context.dataStore.data.map { preferences ->
        preferences[LIGHT_THRESHOLD_KEY] ?: DEFAULT_LIGHT_THRESHOLD
    }
    
    suspend fun setLightThreshold(threshold: Float) {
        context.dataStore.edit { preferences ->
            preferences[LIGHT_THRESHOLD_KEY] = threshold
        }
    }
    
    // 步长
    val stepLength: Flow<Float> = context.dataStore.data.map { preferences ->
        preferences[STEP_LENGTH_KEY] ?: DEFAULT_STEP_LENGTH
    }
    
    suspend fun setStepLength(length: Float) {
        context.dataStore.edit { preferences ->
            preferences[STEP_LENGTH_KEY] = length
        }
    }
    
    // 体重
    val weight: Flow<Float> = context.dataStore.data.map { preferences ->
        preferences[WEIGHT_KEY] ?: DEFAULT_WEIGHT
    }
    
    suspend fun setWeight(weight: Float) {
        context.dataStore.edit { preferences ->
            preferences[WEIGHT_KEY] = weight
        }
    }
    
    // 身高
    val height: Flow<Float> = context.dataStore.data.map { preferences ->
        preferences[HEIGHT_KEY] ?: DEFAULT_HEIGHT
    }
    
    suspend fun setHeight(height: Float) {
        context.dataStore.edit { preferences ->
            preferences[HEIGHT_KEY] = height
        }
    }
    
    // 首次启动
    val firstLaunch: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[FIRST_LAUNCH_KEY] ?: DEFAULT_FIRST_LAUNCH
    }
    
    suspend fun setFirstLaunch(isFirst: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[FIRST_LAUNCH_KEY] = isFirst
        }
    }
    
    // 服务启用状态
    val serviceEnabled: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[SERVICE_ENABLED_KEY] ?: DEFAULT_SERVICE_ENABLED
    }
    
    suspend fun setServiceEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[SERVICE_ENABLED_KEY] = enabled
        }
    }
}
