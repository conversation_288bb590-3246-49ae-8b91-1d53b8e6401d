package com.example.jibuqi.data

import androidx.room.*
import kotlinx.coroutines.flow.Flow

/**
 * 步数数据访问对象
 */
@Dao
interface StepDao {
    
    // 插入或更新当天步数记录
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateStepRecord(stepEntity: StepEntity)
    
    // 获取指定日期的步数记录
    @Query("SELECT * FROM step_records WHERE date = :date LIMIT 1")
    suspend fun getStepRecordByDate(date: String): StepEntity?
    
    // 获取指定日期的步数记录（Flow）
    @Query("SELECT * FROM step_records WHERE date = :date LIMIT 1")
    fun getStepRecordByDateFlow(date: String): Flow<StepEntity?>
    
    // 获取最近N天的步数记录
    @Query("SELECT * FROM step_records ORDER BY date DESC LIMIT :limit")
    suspend fun getRecentStepRecords(limit: Int): List<StepEntity>
    
    // 获取最近N天的步数记录（Flow）
    @Query("SELECT * FROM step_records ORDER BY date DESC LIMIT :limit")
    fun getRecentStepRecordsFlow(limit: Int): Flow<List<StepEntity>>
    
    // 获取指定日期范围的步数记录
    @Query("SELECT * FROM step_records WHERE date BETWEEN :startDate AND :endDate ORDER BY date ASC")
    suspend fun getStepRecordsBetweenDates(startDate: String, endDate: String): List<StepEntity>
    
    // 获取指定日期范围的步数记录（Flow）
    @Query("SELECT * FROM step_records WHERE date BETWEEN :startDate AND :endDate ORDER BY date ASC")
    fun getStepRecordsBetweenDatesFlow(startDate: String, endDate: String): Flow<List<StepEntity>>
    
    // 获取总步数统计
    @Query("SELECT SUM(steps) FROM step_records")
    suspend fun getTotalSteps(): Int?
    
    // 获取平均每日步数
    @Query("SELECT AVG(steps) FROM step_records WHERE steps > 0")
    suspend fun getAverageSteps(): Float?
    
    // 删除指定日期的记录
    @Query("DELETE FROM step_records WHERE date = :date")
    suspend fun deleteStepRecordByDate(date: String)
    
    // 删除所有记录
    @Query("DELETE FROM step_records")
    suspend fun deleteAllStepRecords()
    
    // === 每小时步数相关操作 ===
    
    // 插入每小时步数记录
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHourlyStepRecord(hourlyStepEntity: HourlyStepEntity)
    
    // 获取指定日期的每小时步数
    @Query("SELECT * FROM hourly_steps WHERE dateHour LIKE :datePrefix || '%' ORDER BY dateHour ASC")
    suspend fun getHourlyStepsByDate(datePrefix: String): List<HourlyStepEntity>
    
    // 获取指定日期的每小时步数（Flow）
    @Query("SELECT * FROM hourly_steps WHERE dateHour LIKE :datePrefix || '%' ORDER BY dateHour ASC")
    fun getHourlyStepsByDateFlow(datePrefix: String): Flow<List<HourlyStepEntity>>
    
    // 删除指定日期的每小时记录
    @Query("DELETE FROM hourly_steps WHERE dateHour LIKE :datePrefix || '%'")
    suspend fun deleteHourlyStepsByDate(datePrefix: String)
    
    // 删除所有每小时记录
    @Query("DELETE FROM hourly_steps")
    suspend fun deleteAllHourlySteps()
}
