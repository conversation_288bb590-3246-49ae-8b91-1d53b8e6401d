package com.example.jibuqi.data

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * 步数数据库
 */
@Database(
    entities = [StepEntity::class, HourlyStepEntity::class],
    version = 1,
    exportSchema = false
)
abstract class StepDatabase : RoomDatabase() {
    
    abstract fun stepDao(): StepDao
    
    companion object {
        @Volatile
        private var INSTANCE: StepDatabase? = null
        
        private const val DATABASE_NAME = "step_database"
        
        fun getDatabase(context: Context): StepDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    StepDatabase::class.java,
                    DATABASE_NAME
                )
                    .fallbackToDestructiveMigration() // 开发阶段使用，生产环境应该提供适当的迁移策略
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        // 数据库迁移示例（如果需要）
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 在这里添加数据库结构变更的SQL语句
                // 例如：database.execSQL("ALTER TABLE step_records ADD COLUMN new_column TEXT")
            }
        }
    }
}
