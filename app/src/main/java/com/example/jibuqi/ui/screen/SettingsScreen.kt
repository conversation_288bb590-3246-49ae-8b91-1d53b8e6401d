package com.example.jibuqi.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.jibuqi.R
import com.example.jibuqi.viewmodel.MainViewModel

/**
 * 设置页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    viewModel: MainViewModel,
    modifier: Modifier = Modifier
) {
    val dailyGoal by viewModel.dailyGoal.collectAsStateWithLifecycle()
    val flashlightEnabled by viewModel.flashlightEnabled.collectAsStateWithLifecycle()
    val lightThreshold by viewModel.lightThreshold.collectAsStateWithLifecycle()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    val scrollState = rememberScrollState()
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
            .padding(16.dp)
    ) {
        // 页面标题
        Text(
            text = stringResource(R.string.settings_title),
            style = MaterialTheme.typography.headlineMedium,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        // 每日目标设置
        DailyGoalSetting(
            currentGoal = dailyGoal,
            onGoalChanged = { viewModel.setDailyGoal(it) }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 自动手电筒设置
        FlashlightSettings(
            flashlightEnabled = flashlightEnabled,
            lightThreshold = lightThreshold,
            isFlashlightAvailable = uiState.isFlashlightAvailable,
            currentLightLevel = uiState.lightLevel,
            onFlashlightEnabledChanged = { viewModel.setFlashlightEnabled(it) },
            onLightThresholdChanged = { viewModel.setLightThreshold(it) }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 传感器信息
        SensorInfoSection(viewModel = viewModel)
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 关于应用
        AboutSection()
    }
}

@Composable
private fun DailyGoalSetting(
    currentGoal: Int,
    onGoalChanged: (Int) -> Unit
) {
    var goalText by remember(currentGoal) { mutableStateOf(currentGoal.toString()) }
    var showDialog by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = stringResource(R.string.daily_goal_setting),
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = "$currentGoal ${stringResource(R.string.steps_unit)}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                TextButton(
                    onClick = { showDialog = true }
                ) {
                    Text("修改")
                }
            }
        }
    }
    
    // 目标设置对话框
    if (showDialog) {
        AlertDialog(
            onDismissRequest = { showDialog = false },
            title = { Text(stringResource(R.string.daily_goal_setting)) },
            text = {
                OutlinedTextField(
                    value = goalText,
                    onValueChange = { goalText = it },
                    label = { Text("目标步数") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        goalText.toIntOrNull()?.let { goal ->
                            if (goal > 0) {
                                onGoalChanged(goal)
                                showDialog = false
                            }
                        }
                    }
                ) {
                    Text(stringResource(R.string.save))
                }
            },
            dismissButton = {
                TextButton(onClick = { showDialog = false }) {
                    Text(stringResource(R.string.cancel))
                }
            }
        )
    }
}

@Composable
private fun FlashlightSettings(
    flashlightEnabled: Boolean,
    lightThreshold: Float,
    isFlashlightAvailable: Boolean,
    currentLightLevel: Float,
    onFlashlightEnabledChanged: (Boolean) -> Unit,
    onLightThresholdChanged: (Float) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 自动手电筒开关
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = stringResource(R.string.flashlight_setting),
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = stringResource(R.string.flashlight_desc),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Switch(
                    checked = flashlightEnabled && isFlashlightAvailable,
                    onCheckedChange = onFlashlightEnabledChanged,
                    enabled = isFlashlightAvailable
                )
            }
            
            if (!isFlashlightAvailable) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "手电筒不可用",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
            
            // 光线阈值设置
            if (flashlightEnabled && isFlashlightAvailable) {
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "${stringResource(R.string.light_threshold)}: ${lightThreshold.toInt()} lux",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Slider(
                    value = lightThreshold,
                    onValueChange = onLightThresholdChanged,
                    valueRange = 1f..50f,
                    steps = 49,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Text(
                    text = "当前光线: ${currentLightLevel.toInt()} lux",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun SensorInfoSection(viewModel: MainViewModel) {
    var showSensorInfo by remember { mutableStateOf(false) }
    val sensorInfo = remember { viewModel.checkSensorSupport() }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "传感器信息",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                TextButton(
                    onClick = { showSensorInfo = !showSensorInfo }
                ) {
                    Text(if (showSensorInfo) "隐藏" else "显示")
                }
            }
            
            if (showSensorInfo) {
                Spacer(modifier = Modifier.height(12.dp))
                
                SensorStatusItem(
                    name = "步数传感器",
                    isSupported = sensorInfo.hasStepSensor,
                    info = sensorInfo.stepSensorInfo
                )
                
                SensorStatusItem(
                    name = "光线传感器",
                    isSupported = sensorInfo.hasLightSensor,
                    info = sensorInfo.lightSensorInfo
                )
                
                SensorStatusItem(
                    name = "手电筒",
                    isSupported = sensorInfo.hasFlashlight,
                    info = sensorInfo.flashlightInfo
                )
            }
        }
    }
}

@Composable
private fun SensorStatusItem(
    name: String,
    isSupported: Boolean,
    info: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = if (isSupported) Icons.Default.CheckCircle else Icons.Default.Cancel,
            contentDescription = null,
            tint = if (isSupported) 
                MaterialTheme.colorScheme.primary 
            else 
                MaterialTheme.colorScheme.error,
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = name,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            if (info.isNotEmpty()) {
                Text(
                    text = info,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun AboutSection() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = stringResource(R.string.about_title),
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "计步器 v1.0\n\n" +
                        "功能特性:\n" +
                        "• 实时步数统计\n" +
                        "• 历史记录查看\n" +
                        "• 每日目标设置\n" +
                        "• 自动手电筒功能\n" +
                        "• 距离和卡路里计算",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
