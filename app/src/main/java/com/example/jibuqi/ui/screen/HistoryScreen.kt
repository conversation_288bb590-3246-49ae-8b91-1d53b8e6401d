package com.example.jibuqi.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.jibuqi.R
import com.example.jibuqi.data.StepEntity
import com.example.jibuqi.ui.components.SimpleProgressCircle
import com.example.jibuqi.viewmodel.MainViewModel
import java.text.SimpleDateFormat
import java.util.*

/**
 * 历史记录页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HistoryScreen(
    viewModel: MainViewModel,
    modifier: Modifier = Modifier
) {
    var selectedPeriod by remember { mutableStateOf(HistoryPeriod.WEEK) }
    
    // 获取历史数据
    val historyData by when (selectedPeriod) {
        HistoryPeriod.WEEK -> viewModel.getRecentStepRecords(7).collectAsStateWithLifecycle(initialValue = emptyList())
        HistoryPeriod.MONTH -> viewModel.getRecentStepRecords(30).collectAsStateWithLifecycle(initialValue = emptyList())
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 页面标题和时间段选择
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = stringResource(R.string.history_title),
                style = MaterialTheme.typography.headlineMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            PeriodSelector(
                selectedPeriod = selectedPeriod,
                onPeriodSelected = { selectedPeriod = it }
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        if (historyData.isEmpty()) {
            // 空状态
            EmptyHistoryState()
        } else {
            // 统计概览
            HistoryOverview(
                historyData = historyData,
                period = selectedPeriod
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 历史记录列表
            HistoryList(
                historyData = historyData,
                viewModel = viewModel
            )
        }
    }
}

@Composable
private fun PeriodSelector(
    selectedPeriod: HistoryPeriod,
    onPeriodSelected: (HistoryPeriod) -> Unit
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        HistoryPeriod.values().forEach { period ->
            FilterChip(
                onClick = { onPeriodSelected(period) },
                label = { 
                    Text(
                        text = when (period) {
                            HistoryPeriod.WEEK -> stringResource(R.string.weekly_view)
                            HistoryPeriod.MONTH -> stringResource(R.string.monthly_view)
                        }
                    ) 
                },
                selected = selectedPeriod == period
            )
        }
    }
}

@Composable
private fun HistoryOverview(
    historyData: List<StepEntity>,
    period: HistoryPeriod
) {
    val totalSteps = historyData.sumOf { it.steps }
    val averageSteps = if (historyData.isNotEmpty()) totalSteps / historyData.size else 0
    val totalDistance = historyData.sumOf { it.distance.toDouble() }.toFloat()
    val totalCalories = historyData.sumOf { it.calories.toDouble() }.toFloat()
    val activeDays = historyData.count { it.steps > 0 }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = when (period) {
                    HistoryPeriod.WEEK -> "本周统计"
                    HistoryPeriod.MONTH -> "本月统计"
                },
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                OverviewItem(
                    title = "总步数",
                    value = formatStepNumber(totalSteps),
                    icon = Icons.Default.DirectionsWalk,
                    color = MaterialTheme.colorScheme.primary
                )
                
                OverviewItem(
                    title = "平均步数",
                    value = formatStepNumber(averageSteps),
                    icon = Icons.Default.TrendingUp,
                    color = MaterialTheme.colorScheme.secondary
                )
                
                OverviewItem(
                    title = "活跃天数",
                    value = "$activeDays 天",
                    icon = Icons.Default.CalendarToday,
                    color = MaterialTheme.colorScheme.tertiary
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                OverviewItem(
                    title = "总距离",
                    value = formatDistance(totalDistance),
                    icon = Icons.Default.Route,
                    color = MaterialTheme.colorScheme.primary
                )
                
                OverviewItem(
                    title = "总卡路里",
                    value = "${String.format("%.1f", totalCalories)} 千卡",
                    icon = Icons.Default.LocalFireDepartment,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

@Composable
private fun OverviewItem(
    title: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = color,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = title,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Text(
            text = value,
            style = MaterialTheme.typography.titleSmall.copy(
                fontWeight = FontWeight.SemiBold
            ),
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

@Composable
private fun HistoryList(
    historyData: List<StepEntity>,
    viewModel: MainViewModel
) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(historyData) { stepRecord ->
            HistoryItem(
                stepRecord = stepRecord,
                viewModel = viewModel
            )
        }
    }
}

@Composable
private fun HistoryItem(
    stepRecord: StepEntity,
    viewModel: MainViewModel
) {
    val progress = viewModel.getStepProgress(stepRecord.steps, stepRecord.dailyGoal)
    val dateFormat = SimpleDateFormat("MM月dd日 EEEE", Locale.getDefault())
    val date = try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val parsedDate = inputFormat.parse(stepRecord.date)
        dateFormat.format(parsedDate ?: Date())
    } catch (e: Exception) {
        stepRecord.date
    }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 进度圆环
            SimpleProgressCircle(
                progress = progress,
                modifier = Modifier.size(48.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 日期和步数信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = date,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "${formatStepNumber(stepRecord.steps)} 步",
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontWeight = FontWeight.SemiBold
                    ),
                    color = MaterialTheme.colorScheme.primary
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Text(
                        text = viewModel.formatDistance(stepRecord.distance),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Text(
                        text = "${viewModel.formatCalories(stepRecord.calories)} 千卡",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 目标完成状态
            if (progress >= 1.0f) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "目标完成",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

@Composable
private fun EmptyHistoryState() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Default.HistoryEdu,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = stringResource(R.string.no_data),
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Text(
            text = "开始计步后，这里将显示您的历史记录",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
        )
    }
}

private fun formatStepNumber(steps: Int): String {
    return when {
        steps >= 100000 -> String.format("%.1fW", steps / 10000.0)
        steps >= 10000 -> String.format("%.2fW", steps / 10000.0)
        else -> steps.toString()
    }
}

private fun formatDistance(meters: Float): String {
    return if (meters >= 1000) {
        String.format("%.2f km", meters / 1000)
    } else {
        String.format("%.0f m", meters)
    }
}

enum class HistoryPeriod {
    WEEK, MONTH
}
