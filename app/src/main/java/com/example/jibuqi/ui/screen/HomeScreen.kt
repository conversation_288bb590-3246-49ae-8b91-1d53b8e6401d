package com.example.jibuqi.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.jibuqi.R
import com.example.jibuqi.ui.components.ProgressCircle
import com.example.jibuqi.ui.components.StepCounter
import com.example.jibuqi.viewmodel.MainViewModel
import com.example.jibuqi.viewmodel.MainUiState
import com.example.jibuqi.data.StepEntity

/**
 * 主页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    viewModel: MainViewModel,
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val todayStepRecord by viewModel.todayStepRecord.collectAsStateWithLifecycle()
    val dailyGoal by viewModel.dailyGoal.collectAsStateWithLifecycle()
    val flashlightEnabled by viewModel.flashlightEnabled.collectAsStateWithLifecycle()
    
    val scrollState = rememberScrollState()
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 顶部状态栏
        StatusBar(
            uiState = uiState,
            flashlightEnabled = flashlightEnabled,
            onToggleFlashlight = { viewModel.toggleFlashlight() }
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 主要步数显示
        MainStepDisplay(
            stepRecord = todayStepRecord,
            dailyGoal = dailyGoal,
            viewModel = viewModel
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 统计卡片
        StatsCards(
            stepRecord = todayStepRecord,
            viewModel = viewModel
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // 服务控制按钮
        ServiceControlButton(
            isServiceRunning = uiState.isServiceRunning,
            onStartService = { viewModel.startStepCounterService() },
            onStopService = { viewModel.stopStepCounterService() }
        )
    }
}

@Composable
private fun StatusBar(
    uiState: MainUiState,
    flashlightEnabled: Boolean,
    onToggleFlashlight: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 传感器状态指示器
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            StatusIndicator(
                isActive = uiState.isStepSensorActive,
                icon = Icons.Default.DirectionsWalk,
                contentDescription = "步数传感器"
            )
            StatusIndicator(
                isActive = uiState.isLightSensorActive,
                icon = Icons.Default.WbSunny,
                contentDescription = "光线传感器"
            )
        }
        
        // 手电筒控制
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (uiState.isFlashlightAvailable) {
                Text(
                    text = "${uiState.lightLevel.toInt()} lux",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                IconButton(
                    onClick = onToggleFlashlight,
                    colors = IconButtonDefaults.iconButtonColors(
                        contentColor = if (uiState.isFlashlightOn) 
                            MaterialTheme.colorScheme.primary 
                        else 
                            MaterialTheme.colorScheme.onSurfaceVariant
                    )
                ) {
                    Icon(
                        imageVector = if (uiState.isFlashlightOn)
                            Icons.Default.Flashlight
                        else
                            Icons.Default.FlashlightOff,
                        contentDescription = stringResource(R.string.flashlight_toggle)
                    )
                }
            }
        }
    }
}

@Composable
private fun StatusIndicator(
    isActive: Boolean,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    contentDescription: String
) {
    Icon(
        imageVector = icon,
        contentDescription = contentDescription,
        tint = if (isActive) 
            MaterialTheme.colorScheme.primary 
        else 
            MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f),
        modifier = Modifier.size(20.dp)
    )
}

@Composable
private fun MainStepDisplay(
    stepRecord: StepEntity?,
    dailyGoal: Int,
    viewModel: MainViewModel
) {
    val steps = stepRecord?.steps ?: 0
    val progress = viewModel.getStepProgress(steps, dailyGoal)
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(R.string.today_steps),
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 进度圆环
            ProgressCircle(
                progress = progress,
                modifier = Modifier.size(200.dp)
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = steps.toString(),
                        style = MaterialTheme.typography.displayMedium.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        text = stringResource(R.string.steps_unit),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 目标显示
            Text(
                text = "${stringResource(R.string.daily_goal)}: $dailyGoal ${stringResource(R.string.steps_unit)}",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            // 完成百分比
            Text(
                text = "${(progress * 100).toInt()}% 完成",
                style = MaterialTheme.typography.bodyMedium,
                color = if (progress >= 1.0f) 
                    MaterialTheme.colorScheme.primary 
                else 
                    MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun StatsCards(
    stepRecord: StepEntity?,
    viewModel: MainViewModel
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 距离卡片
        StatCard(
            modifier = Modifier.weight(1f),
            title = stringResource(R.string.distance),
            value = viewModel.formatDistance(stepRecord?.distance ?: 0f),
            icon = Icons.Default.Place,
            color = MaterialTheme.colorScheme.secondary
        )
        
        // 卡路里卡片
        StatCard(
            modifier = Modifier.weight(1f),
            title = stringResource(R.string.calories),
            value = "${viewModel.formatCalories(stepRecord?.calories ?: 0f)} ${stringResource(R.string.cal_unit)}",
            icon = Icons.Default.Whatshot,
            color = MaterialTheme.colorScheme.tertiary
        )
    }
}

@Composable
private fun StatCard(
    title: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = color,
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = value,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.SemiBold
                ),
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun ServiceControlButton(
    isServiceRunning: Boolean,
    onStartService: () -> Unit,
    onStopService: () -> Unit
) {
    Button(
        onClick = if (isServiceRunning) onStopService else onStartService,
        modifier = Modifier.fillMaxWidth(),
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isServiceRunning) 
                MaterialTheme.colorScheme.error 
            else 
                MaterialTheme.colorScheme.primary
        )
    ) {
        Icon(
            imageVector = if (isServiceRunning) Icons.Default.Stop else Icons.Default.PlayArrow,
            contentDescription = null,
            modifier = Modifier.size(18.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = if (isServiceRunning) "停止计步" else "开始计步",
            style = MaterialTheme.typography.titleMedium
        )
    }
}
