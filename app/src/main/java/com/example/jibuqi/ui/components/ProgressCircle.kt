package com.example.jibuqi.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlin.math.cos
import kotlin.math.sin

/**
 * 进度圆环组件
 */
@Composable
fun ProgressCircle(
    progress: Float,
    modifier: Modifier = Modifier,
    strokeWidth: Dp = 12.dp,
    animationDuration: Int = 1000,
    content: @Composable BoxScope.() -> Unit = {}
) {
    val primaryColor = MaterialTheme.colorScheme.primary
    val surfaceVariant = MaterialTheme.colorScheme.surfaceVariant
    
    // 动画进度
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(
            durationMillis = animationDuration,
            easing = EaseOutCubic
        ),
        label = "progress_animation"
    )
    
    // 渐变色
    val gradient = Brush.sweepGradient(
        colors = listOf(
            primaryColor.copy(alpha = 0.3f),
            primaryColor,
            primaryColor.copy(alpha = 0.8f)
        ),
        center = Offset.Unspecified
    )
    
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        val strokeWidthPx = with(LocalDensity.current) { strokeWidth.toPx() }
        
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            val canvasSize = size.minDimension
            val radius = (canvasSize - strokeWidthPx) / 2
            val center = Offset(size.width / 2, size.height / 2)
            
            // 绘制背景圆环
            drawCircle(
                color = surfaceVariant,
                radius = radius,
                center = center,
                style = Stroke(width = strokeWidthPx, cap = StrokeCap.Round)
            )
            
            // 绘制进度圆环
            if (animatedProgress > 0f) {
                drawProgressArc(
                    brush = gradient,
                    progress = animatedProgress,
                    center = center,
                    radius = radius,
                    strokeWidth = strokeWidthPx
                )
            }
            
            // 绘制进度点
            if (animatedProgress > 0f && animatedProgress < 1f) {
                drawProgressDot(
                    color = primaryColor,
                    progress = animatedProgress,
                    center = center,
                    radius = radius,
                    strokeWidth = strokeWidthPx
                )
            }
        }
        
        // 中心内容
        content()
    }
}

/**
 * 绘制进度弧线
 */
private fun DrawScope.drawProgressArc(
    brush: Brush,
    progress: Float,
    center: Offset,
    radius: Float,
    strokeWidth: Float
) {
    val sweepAngle = 360f * progress.coerceIn(0f, 1f)
    
    drawArc(
        brush = brush,
        startAngle = -90f, // 从顶部开始
        sweepAngle = sweepAngle,
        useCenter = false,
        topLeft = Offset(
            center.x - radius,
            center.y - radius
        ),
        size = Size(radius * 2, radius * 2),
        style = Stroke(
            width = strokeWidth,
            cap = StrokeCap.Round
        )
    )
}

/**
 * 绘制进度点
 */
private fun DrawScope.drawProgressDot(
    color: Color,
    progress: Float,
    center: Offset,
    radius: Float,
    strokeWidth: Float
) {
    val angle = -90f + (360f * progress) // 从顶部开始的角度
    val angleRad = Math.toRadians(angle.toDouble())
    
    val dotX = center.x + radius * cos(angleRad).toFloat()
    val dotY = center.y + radius * sin(angleRad).toFloat()
    
    // 绘制外圆（白色边框）
    drawCircle(
        color = Color.White,
        radius = strokeWidth / 2 + 2.dp.toPx(),
        center = Offset(dotX, dotY)
    )
    
    // 绘制内圆（主色）
    drawCircle(
        color = color,
        radius = strokeWidth / 2,
        center = Offset(dotX, dotY)
    )
}

/**
 * 简化版进度圆环（用于小尺寸显示）
 */
@Composable
fun SimpleProgressCircle(
    progress: Float,
    modifier: Modifier = Modifier,
    strokeWidth: Dp = 4.dp,
    size: Dp = 40.dp
) {
    val primaryColor = MaterialTheme.colorScheme.primary
    val surfaceVariant = MaterialTheme.colorScheme.surfaceVariant
    
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(durationMillis = 500),
        label = "simple_progress_animation"
    )
    
    Canvas(
        modifier = modifier.size(size)
    ) {
        val strokeWidthPx = strokeWidth.toPx()
        val radius = (size.toPx() - strokeWidthPx) / 2
        val center = Offset(size.toPx() / 2, size.toPx() / 2)
        
        // 背景圆环
        drawCircle(
            color = surfaceVariant,
            radius = radius,
            center = center,
            style = Stroke(width = strokeWidthPx)
        )
        
        // 进度圆环
        if (animatedProgress > 0f) {
            drawArc(
                color = primaryColor,
                startAngle = -90f,
                sweepAngle = 360f * animatedProgress,
                useCenter = false,
                topLeft = Offset(
                    center.x - radius,
                    center.y - radius
                ),
                size = Size(radius * 2, radius * 2),
                style = Stroke(
                    width = strokeWidthPx,
                    cap = StrokeCap.Round
                )
            )
        }
    }
}

/**
 * 带动画的进度圆环（支持脉冲效果）
 */
@Composable
fun AnimatedProgressCircle(
    progress: Float,
    modifier: Modifier = Modifier,
    strokeWidth: Dp = 12.dp,
    showPulse: Boolean = false,
    content: @Composable BoxScope.() -> Unit = {}
) {
    val primaryColor = MaterialTheme.colorScheme.primary
    val surfaceVariant = MaterialTheme.colorScheme.surfaceVariant
    
    // 进度动画
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(durationMillis = 1000, easing = EaseOutCubic),
        label = "animated_progress"
    )
    
    // 脉冲动画
    val pulseScale by animateFloatAsState(
        targetValue = if (showPulse && progress >= 1f) 1.1f else 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse_animation"
    )
    
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer {
                    scaleX = pulseScale
                    scaleY = pulseScale
                }
        ) {
            val strokeWidthPx = strokeWidth.toPx()
            val radius = (size.minDimension - strokeWidthPx) / 2
            val center = Offset(size.width / 2, size.height / 2)
            
            // 背景圆环
            drawCircle(
                color = surfaceVariant,
                radius = radius,
                center = center,
                style = Stroke(width = strokeWidthPx, cap = StrokeCap.Round)
            )
            
            // 进度圆环
            if (animatedProgress > 0f) {
                val sweepAngle = 360f * animatedProgress
                
                drawArc(
                    color = primaryColor,
                    startAngle = -90f,
                    sweepAngle = sweepAngle,
                    useCenter = false,
                    topLeft = Offset(
                        center.x - radius,
                        center.y - radius
                    ),
                    size = Size(radius * 2, radius * 2),
                    style = Stroke(
                        width = strokeWidthPx,
                        cap = StrokeCap.Round
                    )
                )
            }
        }
        
        content()
    }
}
