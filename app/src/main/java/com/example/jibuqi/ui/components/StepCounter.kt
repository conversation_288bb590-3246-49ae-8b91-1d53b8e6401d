package com.example.jibuqi.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 步数计数器组件
 */
@Composable
fun StepCounter(
    steps: Int,
    modifier: Modifier = Modifier,
    animationDuration: Int = 1000,
    textColor: Color = MaterialTheme.colorScheme.primary,
    showAnimation: Boolean = true
) {
    // 数字滚动动画
    val animatedSteps by animateIntAsState(
        targetValue = steps,
        animationSpec = tween(
            durationMillis = if (showAnimation) animationDuration else 0,
            easing = EaseOutCubic
        ),
        label = "step_counter_animation"
    )
    
    // 缩放动画（当步数增加时）
    var previousSteps by remember { mutableIntStateOf(steps) }
    val scale by animateFloatAsState(
        targetValue = if (steps > previousSteps && showAnimation) 1.1f else 1f,
        animationSpec = tween(
            durationMillis = 200,
            easing = EaseOutBack
        ),
        finishedListener = {
            if (it == 1.1f) {
                // 缩放回原大小
            }
        },
        label = "step_scale_animation"
    )
    
    LaunchedEffect(steps) {
        if (steps != previousSteps) {
            previousSteps = steps
        }
    }
    
    Text(
        text = formatStepNumber(animatedSteps),
        modifier = modifier.scale(scale),
        fontSize = 48.sp,
        fontWeight = FontWeight.Bold,
        color = textColor,
        textAlign = TextAlign.Center
    )
}

/**
 * 带单位的步数显示
 */
@Composable
fun StepCounterWithUnit(
    steps: Int,
    unit: String = "步",
    modifier: Modifier = Modifier,
    animationDuration: Int = 1000,
    textColor: Color = MaterialTheme.colorScheme.primary,
    unitColor: Color = MaterialTheme.colorScheme.onSurfaceVariant
) {
    val animatedSteps by animateIntAsState(
        targetValue = steps,
        animationSpec = tween(
            durationMillis = animationDuration,
            easing = EaseOutCubic
        ),
        label = "step_counter_with_unit_animation"
    )
    
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = formatStepNumber(animatedSteps),
            fontSize = 48.sp,
            fontWeight = FontWeight.Bold,
            color = textColor,
            textAlign = TextAlign.Center
        )
        
        Text(
            text = unit,
            fontSize = 16.sp,
            color = unitColor,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 紧凑型步数显示
 */
@Composable
fun CompactStepCounter(
    steps: Int,
    modifier: Modifier = Modifier,
    textColor: Color = MaterialTheme.colorScheme.onSurface
) {
    val animatedSteps by animateIntAsState(
        targetValue = steps,
        animationSpec = tween(durationMillis = 500),
        label = "compact_step_animation"
    )
    
    Text(
        text = formatStepNumber(animatedSteps),
        modifier = modifier,
        style = MaterialTheme.typography.titleLarge.copy(
            fontWeight = FontWeight.SemiBold
        ),
        color = textColor,
        textAlign = TextAlign.Center
    )
}

/**
 * 步数变化指示器
 */
@Composable
fun StepChangeIndicator(
    currentSteps: Int,
    previousSteps: Int,
    modifier: Modifier = Modifier
) {
    val stepDifference = currentSteps - previousSteps
    
    if (stepDifference > 0) {
        val scale by animateFloatAsState(
            targetValue = 1f,
            animationSpec = keyframes {
                durationMillis = 600
                1.3f at 100 using EaseOutBack
                1f at 600
            },
            label = "step_change_scale"
        )
        
        val alpha by animateFloatAsState(
            targetValue = 0f,
            animationSpec = tween(
                durationMillis = 1000,
                delayMillis = 200
            ),
            label = "step_change_alpha"
        )
        
        Text(
            text = "+$stepDifference",
            modifier = modifier.scale(scale),
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Bold
            ),
            color = MaterialTheme.colorScheme.primary.copy(alpha = alpha)
        )
    }
}

/**
 * 实时步数显示（带动画效果）
 */
@Composable
fun LiveStepCounter(
    steps: Int,
    isActive: Boolean,
    modifier: Modifier = Modifier
) {
    var previousSteps by remember { mutableIntStateOf(steps) }
    
    // 脉冲动画（当计步器活跃时）
    val pulseScale by animateFloatAsState(
        targetValue = if (isActive) 1.05f else 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "live_step_pulse"
    )
    
    // 步数变化动画
    val animatedSteps by animateIntAsState(
        targetValue = steps,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "live_step_animation"
    )
    
    LaunchedEffect(steps) {
        previousSteps = steps
    }
    
    Column(
        modifier = modifier.scale(if (isActive) pulseScale else 1f),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = formatStepNumber(animatedSteps),
            fontSize = 42.sp,
            fontWeight = FontWeight.Bold,
            color = if (isActive) 
                MaterialTheme.colorScheme.primary 
            else 
                MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
        
        // 活跃状态指示器
        if (isActive) {
            Text(
                text = "正在计步...",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
            )
        }
    }
}

/**
 * 格式化步数显示
 */
private fun formatStepNumber(steps: Int): String {
    return when {
        steps >= 100000 -> String.format("%.1fW", steps / 10000.0)
        steps >= 10000 -> String.format("%.2fW", steps / 10000.0)
        else -> steps.toString()
    }
}

/**
 * 步数目标进度显示
 */
@Composable
fun StepGoalProgress(
    currentSteps: Int,
    goalSteps: Int,
    modifier: Modifier = Modifier
) {
    val progress = if (goalSteps > 0) {
        (currentSteps.toFloat() / goalSteps.toFloat()).coerceAtMost(1f)
    } else {
        0f
    }
    
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(durationMillis = 1000, easing = EaseOutCubic),
        label = "goal_progress_animation"
    )
    
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = formatStepNumber(currentSteps),
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = "/ ${formatStepNumber(goalSteps)}",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        LinearProgressIndicator(
            progress = { animatedProgress },
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp),
            color = MaterialTheme.colorScheme.primary,
            trackColor = MaterialTheme.colorScheme.surfaceVariant,
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = "${(animatedProgress * 100).toInt()}% 完成",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
